"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalManager = void 0;
const BaseComponent_1 = require("../common/BaseComponent");
const TerminalInterface_1 = require("./TerminalInterface");
const MessageRenderer_1 = require("./MessageRenderer");
const InputHandler_1 = require("./InputHandler");
const ToolConfirmationHandler_1 = require("./ToolConfirmationHandler");
const uuid_1 = require("uuid");
class TerminalManager extends BaseComponent_1.BaseComponent {
    configManager;
    terminalInterface;
    messageRenderer;
    inputHandler;
    toolConfirmationHandler;
    currentSession = null;
    constructor(state, config, configManager) {
        super(state, config);
        this.configManager = configManager;
        this.terminalInterface = new TerminalInterface_1.TerminalInterface(state, config);
        this.messageRenderer = new MessageRenderer_1.MessageRenderer(state, config);
        this.inputHandler = new InputHandler_1.InputHandler(state, config);
        this.toolConfirmationHandler = new ToolConfirmationHandler_1.ToolConfirmationHandler(state, config);
    }
    async render() {
        await this.initializeSession();
        await this.terminalInterface.render();
        await this.startConversationLoop();
    }
    async initializeSession() {
        this.currentSession = {
            id: (0, uuid_1.v4)(),
            startTime: new Date(),
            messages: [],
            provider: this.state.currentProvider,
            model: this.state.currentModel,
        };
        // Add welcome message
        const welcomeMessage = {
            id: (0, uuid_1.v4)(),
            role: 'system',
            content: this.createWelcomeMessage(),
            timestamp: new Date(),
        };
        this.currentSession.messages.push(welcomeMessage);
        await this.messageRenderer.renderMessage(welcomeMessage);
    }
    createWelcomeMessage() {
        const config = this.configManager.getConfig();
        const providerConfig = this.configManager.getProviderConfig();
        return `Welcome to AI CLI Terminal!

🤖 Provider: ${this.state.currentProvider}
📋 Model: ${this.state.currentModel}
🔧 Tools: ${this.getEnabledToolsCount()} enabled

Type your message or use these commands:
• /help - Show available commands
• /history - View conversation history
• /clear - Clear current conversation
• /switch - Switch AI provider
• /config - Open configuration
• /exit - Exit terminal

Ready to assist you!`;
    }
    getEnabledToolsCount() {
        const tools = this.configManager.getConfig().tools;
        let count = 0;
        if (tools.enableFileTools)
            count += 7; // File system tools
        if (tools.enableShellTools)
            count += 1; // Shell tool
        if (tools.enableWebTools)
            count += 2; // Web tools
        if (tools.enableMemoryTools)
            count += 1; // Memory tool
        return count;
    }
    async startConversationLoop() {
        while (true) {
            try {
                const userInput = await this.inputHandler.getInput();
                if (await this.handleCommand(userInput)) {
                    continue; // Command was handled, continue loop
                }
                // Process as regular message
                await this.processUserMessage(userInput);
            }
            catch (error) {
                if (error instanceof Error && error.message === 'EXIT_TERMINAL') {
                    break;
                }
                this.utils.showError(`Error: ${error}`);
                await this.utils.waitForKeyPress();
            }
        }
    }
    async handleCommand(input) {
        if (!input.startsWith('/')) {
            return false;
        }
        const [command, ...args] = input.slice(1).split(' ');
        switch (command.toLowerCase()) {
            case 'help':
                await this.showHelp();
                return true;
            case 'history':
                await this.showHistory();
                return true;
            case 'clear':
                await this.clearConversation();
                return true;
            case 'switch':
                await this.switchProvider();
                return true;
            case 'config':
                this.updateState({ currentView: 'config' });
                throw new Error('EXIT_TERMINAL');
            case 'exit':
                await this.exitTerminal();
                throw new Error('EXIT_TERMINAL');
            default:
                this.utils.showError(`Unknown command: /${command}`);
                await this.utils.waitForKeyPress();
                return true;
        }
    }
    async processUserMessage(input) {
        const userMessage = {
            id: (0, uuid_1.v4)(),
            role: 'user',
            content: input,
            timestamp: new Date(),
        };
        this.currentSession.messages.push(userMessage);
        await this.messageRenderer.renderMessage(userMessage);
        // TODO: Send to AI provider and handle response
        this.utils.showInfo('AI response processing will be implemented in the next phase...');
        await this.utils.waitForKeyPress();
    }
    async showHelp() {
        const helpText = `
🔧 Available Commands:

/help     - Show this help message
/history  - View conversation history
/clear    - Clear current conversation
/switch   - Switch AI provider
/config   - Open configuration menu
/exit     - Exit terminal

🛠️ Tool Features:
• File operations (read, write, edit, search)
• Shell command execution
• Web content fetching and search
• Persistent memory management
• Model Context Protocol (MCP) support

💡 Tips:
• Use natural language to describe what you want to do
• The AI can use tools to help with file operations, web searches, etc.
• Tool execution requires confirmation unless disabled in preferences
`;
        console.log(this.utils.colorize(helpText, this.config.theme.info));
        await this.utils.waitForKeyPress();
    }
    async showHistory() {
        if (!this.currentSession || this.currentSession.messages.length <= 1) {
            this.utils.showInfo('No conversation history yet.');
            await this.utils.waitForKeyPress();
            return;
        }
        console.log(this.utils.formatHeader('📜 Conversation History'));
        console.log();
        for (const message of this.currentSession.messages) {
            if (message.role !== 'system') {
                await this.messageRenderer.renderMessage(message, true);
            }
        }
        await this.utils.waitForKeyPress();
    }
    async clearConversation() {
        const confirmed = await this.confirmAction('Clear current conversation?');
        if (confirmed) {
            await this.initializeSession();
            this.utils.showSuccess('Conversation cleared');
            await this.utils.waitForKeyPress();
        }
    }
    async switchProvider() {
        this.updateState({ currentView: 'auth' });
        throw new Error('EXIT_TERMINAL');
    }
    async exitTerminal() {
        // Save session to history
        if (this.currentSession && this.currentSession.messages.length > 1) {
            await this.configManager.addToHistory(this.currentSession);
        }
        this.utils.showInfo('Session saved. Goodbye! 👋');
        process.exit(0);
    }
    getCurrentSession() {
        return this.currentSession;
    }
    async addMessage(message) {
        if (this.currentSession) {
            this.currentSession.messages.push(message);
            await this.messageRenderer.renderMessage(message);
        }
    }
    async confirmToolExecution(toolName, parameters) {
        return await this.toolConfirmationHandler.confirmToolExecution(toolName, parameters);
    }
    async startNewConversation() {
        this.currentSession = {
            id: (0, uuid_1.v4)(),
            startTime: new Date(),
            messages: [],
            provider: this.state.currentProvider,
            model: this.state.currentModel,
        };
        // Add welcome message
        const welcomeMessage = {
            id: (0, uuid_1.v4)(),
            role: 'system',
            content: this.createWelcomeMessage(),
            timestamp: new Date(),
        };
        this.currentSession.messages.push(welcomeMessage);
        await this.messageRenderer.renderMessage(welcomeMessage);
    }
    async loadConversation(conversationId) {
        // In a real implementation, this would load the conversation from storage
        // For now, just start a new conversation
        await this.startNewConversation();
        this.updateState({ conversationId });
    }
}
exports.TerminalManager = TerminalManager;
//# sourceMappingURL=TerminalManager.js.map