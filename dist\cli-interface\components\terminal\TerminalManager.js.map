{"version": 3, "file": "TerminalManager.js", "sourceRoot": "", "sources": ["../../../../src/cli-interface/components/terminal/TerminalManager.ts"], "names": [], "mappings": ";;;AAAA,2DAAwD;AACxD,2DAAwD;AACxD,uDAAoD;AACpD,iDAA8C;AAC9C,uEAAoE;AAGpE,+BAAoC;AAEpC,MAAa,eAAgB,SAAQ,6BAAa;IACxC,aAAa,CAAgB;IAC7B,iBAAiB,CAAoB;IACrC,eAAe,CAAkB;IACjC,YAAY,CAAe;IAC3B,uBAAuB,CAA0B;IACjD,cAAc,GAA2B,IAAI,CAAC;IAEtD,YAAY,KAAe,EAAE,MAAiB,EAAE,aAA4B;QAC1E,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,iBAAiB,GAAG,IAAI,qCAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC9D,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC1D,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACpD,IAAI,CAAC,uBAAuB,GAAG,IAAI,iDAAuB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAC5E,CAAC;IAEM,KAAK,CAAC,MAAM;QACjB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/B,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;QACtC,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC,cAAc,GAAG;YACpB,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;YACpC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;SAC/B,CAAC;QAEF,sBAAsB;QACtB,MAAM,cAAc,GAAoB;YACtC,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,IAAI,CAAC,oBAAoB,EAAE;YACpC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAClD,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;IAC3D,CAAC;IAEO,oBAAoB;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAC9C,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;QAE9D,OAAO;;eAEI,IAAI,CAAC,KAAK,CAAC,eAAe;YAC7B,IAAI,CAAC,KAAK,CAAC,YAAY;YACvB,IAAI,CAAC,oBAAoB,EAAE;;;;;;;;;;qBAUlB,CAAC;IACpB,CAAC;IAEO,oBAAoB;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC;QACnD,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,KAAK,CAAC,eAAe;YAAE,KAAK,IAAI,CAAC,CAAC,CAAC,oBAAoB;QAC3D,IAAI,KAAK,CAAC,gBAAgB;YAAE,KAAK,IAAI,CAAC,CAAC,CAAC,aAAa;QACrD,IAAI,KAAK,CAAC,cAAc;YAAE,KAAK,IAAI,CAAC,CAAC,CAAC,YAAY;QAClD,IAAI,KAAK,CAAC,iBAAiB;YAAE,KAAK,IAAI,CAAC,CAAC,CAAC,cAAc;QACvD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,qBAAqB;QACjC,OAAO,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAErD,IAAI,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC;oBACxC,SAAS,CAAC,qCAAqC;gBACjD,CAAC;gBAED,6BAA6B;gBAC7B,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YAE3C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,eAAe,EAAE,CAAC;oBAChE,MAAM;gBACR,CAAC;gBAED,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,KAAK,EAAE,CAAC,CAAC;gBACxC,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YACrC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,KAAa;QACvC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAErD,QAAQ,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;YAC9B,KAAK,MAAM;gBACT,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACtB,OAAO,IAAI,CAAC;YAEd,KAAK,SAAS;gBACZ,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC;YAEd,KAAK,OAAO;gBACV,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC/B,OAAO,IAAI,CAAC;YAEd,KAAK,QAAQ;gBACX,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;gBAC5B,OAAO,IAAI,CAAC;YAEd,KAAK,QAAQ;gBACX,IAAI,CAAC,WAAW,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAC5C,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;YAEnC,KAAK,MAAM;gBACT,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC1B,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;YAEnC;gBACE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,qBAAqB,OAAO,EAAE,CAAC,CAAC;gBACrD,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;gBACnC,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,KAAa;QAC5C,MAAM,WAAW,GAAoB;YACnC,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,cAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAChD,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAEtD,gDAAgD;QAChD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,iEAAiE,CAAC,CAAC;QACvF,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,QAAQ;QACpB,MAAM,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;CAqBpB,CAAC;QAEE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QACnE,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,WAAW;QACvB,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACrE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,8BAA8B,CAAC,CAAC;YACpD,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YACnC,OAAO;QACT,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,yBAAyB,CAAC,CAAC,CAAC;QAChE,OAAO,CAAC,GAAG,EAAE,CAAC;QAEd,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YACnD,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC9B,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QAED,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,6BAA6B,CAAC,CAAC;QAC1E,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC/B,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC;YAC/C,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;QACrC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC,WAAW,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC;QAC1C,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,0BAA0B;QAC1B,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnE,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,4BAA4B,CAAC,CAAC;QAClD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAEM,iBAAiB;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,OAAwB;QAC9C,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3C,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,oBAAoB,CAAC,QAAgB,EAAE,UAA+B;QACjF,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IACvF,CAAC;IAEM,KAAK,CAAC,oBAAoB;QAC/B,IAAI,CAAC,cAAc,GAAG;YACpB,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe;YACpC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;SAC/B,CAAC;QAEF,sBAAsB;QACtB,MAAM,cAAc,GAAoB;YACtC,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,IAAI,CAAC,oBAAoB,EAAE;YACpC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAClD,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;IAC3D,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,cAAsB;QAClD,0EAA0E;QAC1E,yCAAyC;QACzC,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAClC,IAAI,CAAC,WAAW,CAAC,EAAE,cAAc,EAAE,CAAC,CAAC;IACvC,CAAC;CACF;AA1QD,0CA0QC"}