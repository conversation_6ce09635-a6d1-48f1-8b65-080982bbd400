import { AppConfig, ProviderConfiguration, SupportedProvider } from '../types';
import { ProviderConfig } from '../providers/BaseProvider';
export declare class ConfigManager {
    private config;
    private configPath;
    private configDir;
    constructor(configDir?: string);
    private expandPath;
    private ensureConfigDir;
    loadConfig(): Promise<void>;
    saveConfig(): Promise<void>;
    private mergeConfig;
    getConfig(): AppConfig;
    updateConfig(updates: Partial<AppConfig>): Promise<void>;
    configureProvider(providerName: SupportedProvider, config: Partial<ProviderConfiguration>): Promise<void>;
    getProviderConfig(providerName?: string): ProviderConfiguration;
    getProviderConfigForClient(providerName?: string): ProviderConfig;
    isProviderConfigured(providerName: string): boolean;
    getConfiguredProviders(): string[];
    setDefaultProvider(providerName: string): Promise<void>;
    getDefaultProvider(): string;
    isToolCategoryEnabled(category: string): boolean;
    requiresToolConfirmation(): boolean;
    isSandboxMode(): boolean;
    getMaxFileSize(): number;
    isCommandAllowed(command: string): boolean;
    isPathAllowed(filePath: string): boolean;
    isDomainAllowed(domain: string): boolean;
    getUIConfig(): {
        theme: string;
        showTimestamps: boolean;
        showTokenCounts: boolean;
        maxHistorySize: number;
        autoSave: boolean;
        streamResponses: boolean;
    };
    updateUIConfig(updates: Partial<typeof this.config.ui>): Promise<void>;
    getConfigPath(): string;
    getConfigDir(): string;
    resetConfig(): Promise<void>;
    setProvider(providerName: string, config: ProviderConfiguration): Promise<void>;
    setProviderConfig(providerName: string, config: Partial<ProviderConfiguration>): Promise<void>;
    removeProviderConfig(providerName: string): Promise<void>;
    resetToDefaults(): Promise<void>;
    exportConfig(): Promise<string>;
    importConfig(configJson: string): Promise<void>;
}
//# sourceMappingURL=ConfigManager.d.ts.map