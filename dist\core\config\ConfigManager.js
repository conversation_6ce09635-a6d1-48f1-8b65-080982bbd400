"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const os = __importStar(require("os"));
const types_1 = require("../types");
class ConfigManager {
    config;
    configPath;
    configDir;
    constructor(configDir) {
        this.configDir = configDir || this.expandPath(types_1.DEFAULT_APP_CONFIG.configDir);
        this.configPath = path.join(this.configDir, 'config.json');
        this.config = { ...types_1.DEFAULT_APP_CONFIG };
        this.loadConfig();
    }
    expandPath(filePath) {
        if (filePath.startsWith('~')) {
            return path.join(os.homedir(), filePath.slice(1));
        }
        return filePath;
    }
    async ensureConfigDir() {
        try {
            await fs.ensureDir(this.configDir);
        }
        catch (error) {
            throw new types_1.ConfigError(`Failed to create config directory: ${this.configDir}`, { error });
        }
    }
    async loadConfig() {
        try {
            if (await fs.pathExists(this.configPath)) {
                const configData = await fs.readJson(this.configPath);
                this.config = this.mergeConfig(types_1.DEFAULT_APP_CONFIG, configData);
            }
            else {
                // Create default config file
                await this.saveConfig();
            }
        }
        catch (error) {
            console.warn(`Failed to load config from ${this.configPath}, using defaults:`, error);
            this.config = { ...types_1.DEFAULT_APP_CONFIG };
        }
    }
    async saveConfig() {
        try {
            await this.ensureConfigDir();
            await fs.writeJson(this.configPath, this.config, { spaces: 2 });
        }
        catch (error) {
            throw new types_1.ConfigError(`Failed to save config to ${this.configPath}`, { error });
        }
    }
    mergeConfig(defaultConfig, userConfig) {
        return {
            ...defaultConfig,
            ...userConfig,
            providers: {
                ...defaultConfig.providers,
                ...userConfig.providers
            },
            tools: {
                ...defaultConfig.tools,
                ...userConfig.tools
            },
            security: {
                ...defaultConfig.security,
                ...userConfig.security
            },
            ui: {
                ...defaultConfig.ui,
                ...userConfig.ui
            }
        };
    }
    getConfig() {
        return { ...this.config };
    }
    async updateConfig(updates) {
        this.config = this.mergeConfig(this.config, updates);
        await this.saveConfig();
    }
    // Provider management
    async configureProvider(providerName, config) {
        if (!types_1.SUPPORTED_PROVIDER_NAMES.includes(providerName)) {
            throw new types_1.ConfigError(`Unsupported provider: ${providerName}`);
        }
        const providerConfig = {
            apiKey: config.apiKey || '',
            baseUrl: config.baseUrl || undefined,
            defaultModel: config.defaultModel || types_1.DEFAULT_PROVIDER_MODELS[providerName],
            maxTokens: config.maxTokens || 4000,
            temperature: config.temperature ?? 0.7,
            systemPrompt: config.systemPrompt || undefined,
            enabled: config.enabled ?? true
        };
        if (!providerConfig.apiKey) {
            throw new types_1.ConfigError(`API key is required for ${providerName}`);
        }
        this.config.providers[providerName] = providerConfig;
        await this.saveConfig();
    }
    getProviderConfig(providerName) {
        const provider = providerName || this.config.defaultProvider;
        const config = this.config.providers[provider];
        if (!config) {
            throw new types_1.ConfigError(`Provider ${provider} is not configured`);
        }
        return { ...config };
    }
    getProviderConfigForClient(providerName) {
        const config = this.getProviderConfig(providerName);
        return {
            apiKey: config.apiKey,
            baseUrl: config.baseUrl || undefined,
            defaultModel: config.defaultModel,
            maxTokens: config.maxTokens || undefined,
            temperature: config.temperature || undefined,
            systemPrompt: config.systemPrompt || undefined
        };
    }
    isProviderConfigured(providerName) {
        const config = this.config.providers[providerName];
        return !!(config && config.apiKey && config.enabled);
    }
    getConfiguredProviders() {
        return Object.keys(this.config.providers).filter(provider => this.isProviderConfigured(provider));
    }
    async setDefaultProvider(providerName) {
        if (!this.isProviderConfigured(providerName)) {
            throw new types_1.ConfigError(`Cannot set unconfigured provider as default: ${providerName}`);
        }
        this.config.defaultProvider = providerName;
        await this.saveConfig();
    }
    getDefaultProvider() {
        return this.config.defaultProvider;
    }
    // Tool configuration
    isToolCategoryEnabled(category) {
        switch (category) {
            case 'filesystem':
                return this.config.tools.enableFileTools;
            case 'execution':
                return this.config.tools.enableShellTools;
            case 'web':
                return this.config.tools.enableWebTools;
            case 'memory':
                return this.config.tools.enableMemoryTools;
            default:
                return true;
        }
    }
    requiresToolConfirmation() {
        return this.config.tools.requireConfirmation;
    }
    isSandboxMode() {
        return this.config.tools.sandboxMode;
    }
    getMaxFileSize() {
        const sizeStr = this.config.tools.maxFileSize;
        const match = sizeStr.match(/^(\d+)(KB|MB|GB)?$/i);
        if (!match) {
            return 10 * 1024 * 1024; // Default 10MB
        }
        const size = parseInt(match[1]);
        const unit = (match[2] || 'B').toUpperCase();
        switch (unit) {
            case 'KB':
                return size * 1024;
            case 'MB':
                return size * 1024 * 1024;
            case 'GB':
                return size * 1024 * 1024 * 1024;
            default:
                return size;
        }
    }
    isCommandAllowed(command) {
        const { allowedCommands, blockedCommands } = this.config.tools;
        // Check if explicitly blocked
        if (blockedCommands.some(blocked => command.startsWith(blocked))) {
            return false;
        }
        // If allowedCommands is empty, allow all (except blocked)
        if (allowedCommands.length === 0) {
            return true;
        }
        // Check if explicitly allowed
        return allowedCommands.some(allowed => command.startsWith(allowed));
    }
    // Security configuration
    isPathAllowed(filePath) {
        const { allowedPaths, blockedPaths } = this.config.security;
        const normalizedPath = path.resolve(filePath);
        // Check if explicitly blocked
        if (blockedPaths.some(blocked => normalizedPath.startsWith(blocked))) {
            return false;
        }
        // If allowedPaths is empty, allow all (except blocked)
        if (allowedPaths.length === 0) {
            return true;
        }
        // Check if explicitly allowed
        return allowedPaths.some(allowed => normalizedPath.startsWith(allowed));
    }
    isDomainAllowed(domain) {
        const { allowedDomains, blockedDomains } = this.config.security;
        // Check if explicitly blocked
        if (blockedDomains.includes(domain)) {
            return false;
        }
        // If allowedDomains is empty, allow all (except blocked)
        if (allowedDomains.length === 0) {
            return true;
        }
        // Check if explicitly allowed
        return allowedDomains.includes(domain);
    }
    // UI configuration
    getUIConfig() {
        return { ...this.config.ui };
    }
    async updateUIConfig(updates) {
        this.config.ui = { ...this.config.ui, ...updates };
        await this.saveConfig();
    }
    // Utility methods
    getConfigPath() {
        return this.configPath;
    }
    getConfigDir() {
        return this.configDir;
    }
    async resetConfig() {
        this.config = { ...types_1.DEFAULT_APP_CONFIG };
        await this.saveConfig();
    }
    async setProvider(providerName, config) {
        this.config.providers[providerName] = config;
        await this.saveConfig();
    }
    async setProviderConfig(providerName, config) {
        if (!this.config.providers[providerName]) {
            this.config.providers[providerName] = {
                apiKey: '',
                defaultModel: '',
                ...config
            };
        }
        else {
            this.config.providers[providerName] = {
                ...this.config.providers[providerName],
                ...config
            };
        }
        await this.saveConfig();
    }
    async removeProviderConfig(providerName) {
        delete this.config.providers[providerName];
        // If this was the default provider, reset to first available
        if (this.config.defaultProvider === providerName) {
            const remainingProviders = Object.keys(this.config.providers);
            this.config.defaultProvider = remainingProviders.length > 0 ? remainingProviders[0] : 'openai';
        }
        await this.saveConfig();
    }
    async resetToDefaults() {
        this.config = { ...types_1.DEFAULT_APP_CONFIG };
        await this.saveConfig();
    }
    async exportConfig() {
        return JSON.stringify(this.config, null, 2);
    }
    async importConfig(configJson) {
        try {
            const importedConfig = JSON.parse(configJson);
            this.config = this.mergeConfig(types_1.DEFAULT_APP_CONFIG, importedConfig);
            await this.saveConfig();
        }
        catch (error) {
            throw new types_1.ConfigError('Failed to import config: Invalid JSON', { error });
        }
    }
}
exports.ConfigManager = ConfigManager;
//# sourceMappingURL=ConfigManager.js.map