"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationManager = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const uuid_1 = require("uuid");
class ConversationManager {
    configManager;
    conversationsDir;
    currentConversation = null;
    eventListeners = new Map();
    constructor(configManager) {
        this.configManager = configManager;
        this.conversationsDir = path.join(configManager.getConfigDir(), 'conversations');
        this.ensureConversationsDir();
    }
    async ensureConversationsDir() {
        await fs.ensureDir(this.conversationsDir);
    }
    async createConversation(provider, model, title) {
        const conversation = {
            id: (0, uuid_1.v4)(),
            title: title || this.generateTitle(),
            provider,
            model,
            messages: [],
            createdAt: new Date(),
            updatedAt: new Date()
        };
        await this.saveConversation(conversation);
        this.currentConversation = conversation;
        this.emitEvent({
            type: 'conversation.started',
            timestamp: new Date(),
            data: {
                conversationId: conversation.id
            }
        });
        return conversation;
    }
    async loadConversation(conversationId) {
        try {
            const filePath = path.join(this.conversationsDir, `${conversationId}.json`);
            if (!(await fs.pathExists(filePath))) {
                return null;
            }
            const conversation = await fs.readJson(filePath);
            // Convert date strings back to Date objects
            conversation.createdAt = new Date(conversation.createdAt);
            conversation.updatedAt = new Date(conversation.updatedAt);
            conversation.messages = conversation.messages.map((msg) => ({
                ...msg,
                timestamp: new Date(msg.timestamp)
            }));
            this.currentConversation = conversation;
            return conversation;
        }
        catch (error) {
            console.error(`Failed to load conversation ${conversationId}:`, error);
            return null;
        }
    }
    async saveConversation(conversation) {
        try {
            const filePath = path.join(this.conversationsDir, `${conversation.id}.json`);
            conversation.updatedAt = new Date();
            await fs.writeJson(filePath, conversation, { spaces: 2 });
        }
        catch (error) {
            console.error(`Failed to save conversation ${conversation.id}:`, error);
            throw error;
        }
    }
    async deleteConversation(conversationId) {
        try {
            const filePath = path.join(this.conversationsDir, `${conversationId}.json`);
            if (await fs.pathExists(filePath)) {
                await fs.remove(filePath);
                if (this.currentConversation?.id === conversationId) {
                    this.currentConversation = null;
                }
                this.emitEvent({
                    type: 'conversation.ended',
                    timestamp: new Date(),
                    data: {
                        conversationId
                    }
                });
                return true;
            }
            return false;
        }
        catch (error) {
            console.error(`Failed to delete conversation ${conversationId}:`, error);
            return false;
        }
    }
    async addMessage(message) {
        if (!this.currentConversation) {
            throw new Error('No active conversation');
        }
        this.currentConversation.messages.push(message);
        await this.saveConversation(this.currentConversation);
        this.emitEvent({
            type: message.role === 'user' ? 'message.sent' : 'message.received',
            timestamp: new Date(),
            data: {
                conversationId: this.currentConversation.id,
                message
            }
        });
    }
    async updateMessage(messageId, updates) {
        if (!this.currentConversation) {
            return false;
        }
        const messageIndex = this.currentConversation.messages.findIndex(msg => msg.id === messageId);
        if (messageIndex === -1) {
            return false;
        }
        this.currentConversation.messages[messageIndex] = {
            ...this.currentConversation.messages[messageIndex],
            ...updates
        };
        await this.saveConversation(this.currentConversation);
        return true;
    }
    async getConversationHistory(limit) {
        try {
            const files = await fs.readdir(this.conversationsDir);
            const jsonFiles = files.filter(file => file.endsWith('.json'));
            const summaries = [];
            for (const file of jsonFiles) {
                try {
                    const filePath = path.join(this.conversationsDir, file);
                    const conversation = await fs.readJson(filePath);
                    summaries.push({
                        id: conversation.id,
                        title: conversation.title,
                        provider: conversation.provider,
                        model: conversation.model,
                        messageCount: conversation.messages.length,
                        createdAt: new Date(conversation.createdAt),
                        updatedAt: new Date(conversation.updatedAt)
                    });
                }
                catch (error) {
                    console.error(`Failed to read conversation file ${file}:`, error);
                }
            }
            // Sort by updated date (most recent first)
            summaries.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
            return limit ? summaries.slice(0, limit) : summaries;
        }
        catch (error) {
            console.error('Failed to get conversation history:', error);
            return [];
        }
    }
    getCurrentConversation() {
        return this.currentConversation;
    }
    async listConversations() {
        return this.getConversationHistory();
    }
    async getConversation(conversationId) {
        try {
            const conversationPath = path.join(this.conversationsDir, `${conversationId}.json`);
            if (await fs.pathExists(conversationPath)) {
                const data = await fs.readJson(conversationPath);
                return {
                    ...data,
                    createdAt: new Date(data.createdAt),
                    updatedAt: new Date(data.updatedAt)
                };
            }
            return null;
        }
        catch (error) {
            console.error(`Failed to load conversation ${conversationId}:`, error);
            return null;
        }
    }
    async searchConversations(query) {
        const allConversations = await this.getConversationHistory();
        const lowerQuery = query.toLowerCase();
        return allConversations.filter(conv => conv.title.toLowerCase().includes(lowerQuery) ||
            conv.provider.toLowerCase().includes(lowerQuery) ||
            conv.model.toLowerCase().includes(lowerQuery));
    }
    async exportConversation(conversationId) {
        const conversation = await this.loadConversation(conversationId);
        if (!conversation) {
            return null;
        }
        return JSON.stringify(conversation, null, 2);
    }
    async importConversation(conversationData) {
        try {
            const conversation = JSON.parse(conversationData);
            // Validate conversation structure
            if (!conversation.id || !conversation.messages || !Array.isArray(conversation.messages)) {
                throw new Error('Invalid conversation format');
            }
            // Generate new ID to avoid conflicts
            conversation.id = (0, uuid_1.v4)();
            conversation.createdAt = new Date(conversation.createdAt);
            conversation.updatedAt = new Date();
            conversation.messages = conversation.messages.map((msg) => ({
                ...msg,
                timestamp: new Date(msg.timestamp)
            }));
            await this.saveConversation(conversation);
            return conversation;
        }
        catch (error) {
            console.error('Failed to import conversation:', error);
            return null;
        }
    }
    async cleanupOldConversations(maxAge = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - maxAge);
        const conversations = await this.getConversationHistory();
        let deletedCount = 0;
        for (const conv of conversations) {
            if (conv.updatedAt < cutoffDate) {
                const deleted = await this.deleteConversation(conv.id);
                if (deleted) {
                    deletedCount++;
                }
            }
        }
        return deletedCount;
    }
    generateTitle() {
        const now = new Date();
        return `Conversation ${now.toLocaleDateString()} ${now.toLocaleTimeString()}`;
    }
    // Event system
    addEventListener(eventType, listener) {
        if (!this.eventListeners.has(eventType)) {
            this.eventListeners.set(eventType, []);
        }
        this.eventListeners.get(eventType).push(listener);
    }
    removeEventListener(eventType, listener) {
        const listeners = this.eventListeners.get(eventType);
        if (listeners) {
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }
    emitEvent(event) {
        const listeners = this.eventListeners.get(event.type);
        if (listeners) {
            listeners.forEach(listener => listener(event));
        }
        // Also emit to 'all' listeners
        const allListeners = this.eventListeners.get('all');
        if (allListeners) {
            allListeners.forEach(listener => listener(event));
        }
    }
}
exports.ConversationManager = ConversationManager;
//# sourceMappingURL=ConversationManager.js.map