{"version": 3, "file": "ConversationManager.js", "sourceRoot": "", "sources": ["../../../src/core/engine/ConversationManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,6CAA+B;AAC/B,2CAA6B;AAC7B,+BAAoC;AAuBpC,MAAa,mBAAmB;IACtB,aAAa,CAAgB;IAC7B,gBAAgB,CAAS;IACzB,mBAAmB,GAAwB,IAAI,CAAC;IAChD,cAAc,GAAkD,IAAI,GAAG,EAAE,CAAC;IAElF,YAAY,aAA4B;QACtC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,EAAE,eAAe,CAAC,CAAC;QACjF,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAClC,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC5C,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAC7B,QAAgB,EAChB,KAAa,EACb,KAAc;QAEd,MAAM,YAAY,GAAiB;YACjC,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,KAAK,EAAE,KAAK,IAAI,IAAI,CAAC,aAAa,EAAE;YACpC,QAAQ;YACR,KAAK;YACL,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAC1C,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC;QAExC,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,sBAAsB;YAC5B,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,IAAI,EAAE;gBACJ,cAAc,EAAE,YAAY,CAAC,EAAE;aAChC;SACF,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,cAAsB;QAClD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,cAAc,OAAO,CAAC,CAAC;YAE5E,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;gBACrC,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEjD,4CAA4C;YAC5C,YAAY,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC1D,YAAY,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC1D,YAAY,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC;gBAC/D,GAAG,GAAG;gBACN,SAAS,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;aACnC,CAAC,CAAC,CAAC;YAEJ,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC;YACxC,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,cAAc,GAAG,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,YAA0B;QACtD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,YAAY,CAAC,EAAE,OAAO,CAAC,CAAC;YAC7E,YAAY,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YACpC,MAAM,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,YAAY,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,YAAY,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACxE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,cAAsB;QACpD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,cAAc,OAAO,CAAC,CAAC;YAE5E,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClC,MAAM,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAE1B,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,KAAK,cAAc,EAAE,CAAC;oBACpD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAClC,CAAC;gBAED,IAAI,CAAC,SAAS,CAAC;oBACb,IAAI,EAAE,oBAAoB;oBAC1B,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,IAAI,EAAE;wBACJ,cAAc;qBACf;iBACF,CAAC,CAAC;gBAEH,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,cAAc,GAAG,EAAE,KAAK,CAAC,CAAC;YACzE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,OAAoB;QAC1C,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChD,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAEtD,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,kBAAkB;YACnE,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,IAAI,EAAE;gBACJ,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,EAAE;gBAC3C,OAAO;aACR;SACF,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,OAA6B;QACzE,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;QAC9F,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG;YAChD,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,YAAY,CAAC;YAClD,GAAG,OAAO;SACX,CAAC;QAEF,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACtD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,KAAc;QAChD,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACtD,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;YAE/D,MAAM,SAAS,GAA0B,EAAE,CAAC;YAE5C,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;gBAC7B,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;oBACxD,MAAM,YAAY,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEjD,SAAS,CAAC,IAAI,CAAC;wBACb,EAAE,EAAE,YAAY,CAAC,EAAE;wBACnB,KAAK,EAAE,YAAY,CAAC,KAAK;wBACzB,QAAQ,EAAE,YAAY,CAAC,QAAQ;wBAC/B,KAAK,EAAE,YAAY,CAAC,KAAK;wBACzB,YAAY,EAAE,YAAY,CAAC,QAAQ,CAAC,MAAM;wBAC1C,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;wBAC3C,SAAS,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;qBAC5C,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC;YAED,2CAA2C;YAC3C,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;YAExE,OAAO,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEM,sBAAsB;QAC3B,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAEM,KAAK,CAAC,iBAAiB;QAC5B,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAC;IACvC,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,cAAsB;QACjD,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,cAAc,OAAO,CAAC,CAAC;YACpF,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC1C,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;gBACjD,OAAO;oBACL,GAAG,IAAI;oBACP,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;oBACnC,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;iBACpC,CAAC;YACJ,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,cAAc,GAAG,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAAC,KAAa;QAC5C,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC7D,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAEvC,OAAO,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACpC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC7C,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAC9C,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,cAAsB;QACpD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;QACjE,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,gBAAwB;QACtD,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAElD,kCAAkC;YAClC,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACxF,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;YACjD,CAAC;YAED,qCAAqC;YACrC,YAAY,CAAC,EAAE,GAAG,IAAA,SAAM,GAAE,CAAC;YAC3B,YAAY,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC1D,YAAY,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YACpC,YAAY,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC;gBAC/D,GAAG,GAAG;gBACN,SAAS,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;aACnC,CAAC,CAAC,CAAC;YAEJ,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;YAC1C,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,uBAAuB,CAAC,SAAiB,EAAE;QACtD,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC;QAElD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC1D,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;YACjC,IAAI,IAAI,CAAC,SAAS,GAAG,UAAU,EAAE,CAAC;gBAChC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACvD,IAAI,OAAO,EAAE,CAAC;oBACZ,YAAY,EAAE,CAAC;gBACjB,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,aAAa;QACnB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,OAAO,gBAAgB,GAAG,CAAC,kBAAkB,EAAE,IAAI,GAAG,CAAC,kBAAkB,EAAE,EAAE,CAAC;IAChF,CAAC;IAED,eAAe;IACR,gBAAgB,CAAC,SAAiB,EAAE,QAAsC;QAC/E,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrD,CAAC;IAEM,mBAAmB,CAAC,SAAiB,EAAE,QAAsC;QAClF,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC1C,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACf,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,SAAS,CAAC,KAAwB;QACxC,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACtD,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QACjD,CAAC;QAED,+BAA+B;QAC/B,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACpD,IAAI,YAAY,EAAE,CAAC;YACjB,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;CACF;AAtTD,kDAsTC"}