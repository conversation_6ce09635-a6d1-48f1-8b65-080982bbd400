import { ProviderClient, ChatMessage, BaseTool } from '../types';
export interface ProviderConfig {
    apiKey: string;
    baseUrl?: string;
    defaultModel: string;
    maxTokens?: number;
    temperature?: number;
    systemPrompt?: string;
}
export interface ProviderOptions {
    model?: string;
    maxTokens?: number;
    temperature?: number;
    stream?: boolean;
    tools?: BaseTool[];
}
export interface ProviderResponse {
    content: string;
    model: string;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
    toolCalls?: Array<{
        id: string;
        name: string;
        parameters: Record<string, any>;
    }>;
    finishReason?: string;
}
export declare abstract class BaseProvider implements ProviderClient {
    protected config: ProviderConfig;
    abstract name: string;
    constructor(config: ProviderConfig);
    isConfigured(): boolean;
    abstract sendMessage(messages: ChatMessage[], tools?: BaseTool[], options?: ProviderOptions): Promise<ChatMessage>;
    abstract streamMessage?(messages: ChatMessage[], tools?: BaseTool[], options?: ProviderOptions): AsyncGenerator<string, ChatMessage>;
    protected formatMessages(messages: ChatMessage[]): any[] | {
        systemMessage?: string;
        formattedMessages: any[];
    };
    protected formatTools(tools?: BaseTool[]): any[];
    protected createChatMessage(content: string, role?: 'assistant', metadata?: any): ChatMessage;
    protected generateId(): string;
    protected handleError(error: any, context: string): Error;
    protected validateConfig(): void;
    protected getRequestOptions(options?: ProviderOptions): {
        model: string;
        maxTokens: number;
        temperature: number;
        stream: boolean;
    };
    updateConfig(updates: Partial<ProviderConfig>): void;
    getConfig(): ProviderConfig;
    testConnection(): Promise<boolean>;
}
//# sourceMappingURL=BaseProvider.d.ts.map