import { BaseProvider, ProviderConfig } from './BaseProvider';
export declare class ProviderFactory {
    private static providers;
    static createProvider(providerName: string, config: ProviderConfig): BaseProvider;
    static getProvider(providerName: string): BaseProvider | null;
    static hasProvider(providerName: string): boolean;
    static removeProvider(providerName: string): void;
    static clearProviders(): void;
    static getLoadedProviders(): string[];
    static testProvider(providerName: string, config: ProviderConfig): Promise<boolean>;
    static validateProviderConfig(providerName: string, config: Partial<ProviderConfig>): string[];
    static getProviderCapabilities(providerName: string): {
        supportsStreaming: boolean;
        supportsTools: boolean;
        supportsVision: boolean;
        maxTokens: number;
        supportedModels: string[];
    };
    private static configMatches;
    private static isValidUrl;
    static getSupportedProviders(): string[];
    static getProviderInfo(providerName: string): {
        name: string;
        displayName: string;
        models: string[];
        description: string;
    };
    static getAvailableModels(providerName: string, config: ProviderConfig): Promise<string[]>;
}
//# sourceMappingURL=ProviderFactory.d.ts.map