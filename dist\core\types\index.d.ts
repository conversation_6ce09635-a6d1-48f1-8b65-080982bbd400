export interface ChatMessage {
    id: string;
    role: 'user' | 'assistant' | 'system' | 'tool';
    content: string;
    timestamp: Date;
    metadata?: {
        provider?: string;
        model?: string;
        tokens?: number;
        duration?: number;
        toolCallId?: string;
        toolCalls?: ToolCall[];
        usage?: TokenUsage;
        finishReason?: string;
    };
    toolCalls?: ToolCall[];
}
export interface ToolCall {
    id: string;
    name: string;
    parameters: Record<string, any>;
    result?: any;
    error?: string;
}
export interface TokenUsage {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
}
export interface BaseTool {
    name: string;
    description: string;
    parameters: {
        type: 'object';
        properties: Record<string, any>;
        required?: string[];
    };
    requiresConfirmation: boolean;
}
export interface ToolResult {
    success: boolean;
    content: string;
    displayContent?: string;
    error?: string;
    metadata?: Record<string, any>;
}
export interface ProviderClient {
    name: string;
    isConfigured(): boolean;
    sendMessage(messages: ChatMessage[], tools?: BaseTool[], options?: any): Promise<ChatMessage>;
    streamMessage?(messages: ChatMessage[], tools?: BaseTool[], options?: any): AsyncGenerator<string, ChatMessage>;
    testConnection(): Promise<boolean>;
    getAvailableModels(): Promise<string[]>;
}
export interface AppConfig {
    defaultProvider: string;
    configDir: string;
    providers: Record<string, ProviderConfiguration>;
    tools: ToolConfiguration;
    security: SecurityConfiguration;
    ui: UIConfiguration;
}
export interface ProviderConfiguration {
    apiKey: string;
    baseUrl?: string;
    defaultModel: string;
    maxTokens?: number;
    temperature?: number;
    systemPrompt?: string;
    enabled: boolean;
    advanced?: {
        maxTokens?: number;
        temperature?: number;
        systemPrompt?: string;
    };
}
export interface ToolConfiguration {
    enableShellTools: boolean;
    enableWebTools: boolean;
    enableFileTools: boolean;
    enableMemoryTools: boolean;
    requireConfirmation: boolean;
    sandboxMode: boolean;
    maxFileSize: string;
    allowedCommands: string[];
    blockedCommands: string[];
}
export interface SecurityConfiguration {
    requireToolConfirmation: boolean;
    sandboxMode: boolean;
    maxFileSize: number;
    allowedPaths: string[];
    blockedPaths: string[];
    allowedDomains: string[];
    blockedDomains: string[];
}
export interface UIConfiguration {
    theme: string;
    showTimestamps: boolean;
    showTokenCounts: boolean;
    maxHistorySize: number;
    autoSave: boolean;
    streamResponses: boolean;
}
export declare const SUPPORTED_PROVIDERS: Record<string, {
    name: string;
    displayName: string;
    models: string[];
    description: string;
}>;
export declare const SUPPORTED_PROVIDER_NAMES: readonly ["openai", "anthropic", "google", "deepseek"];
export type SupportedProvider = typeof SUPPORTED_PROVIDER_NAMES[number];
export declare const DEFAULT_PROVIDER_MODELS: Record<SupportedProvider, string>;
export declare const DEFAULT_APP_CONFIG: AppConfig;
export declare class AICliError extends Error {
    code: string;
    details?: any;
    constructor(message: string, code?: string, details?: any);
}
export declare class ProviderError extends AICliError {
    constructor(message: string, provider: string, details?: any);
}
export declare class ToolError extends AICliError {
    constructor(message: string, toolName: string, details?: any);
}
export declare class ConfigError extends AICliError {
    constructor(message: string, details?: any);
}
export interface SystemEvent {
    type: string;
    timestamp: Date;
    data: any;
}
export interface ProviderEvent extends SystemEvent {
    type: 'provider.connected' | 'provider.disconnected' | 'provider.error';
    data: {
        provider: string;
        error?: string;
    };
}
export interface ToolEvent extends SystemEvent {
    type: 'tool.executed' | 'tool.failed' | 'tool.confirmed' | 'tool.denied';
    data: {
        toolName: string;
        parameters?: Record<string, any>;
        result?: ToolResult;
        error?: string;
    };
}
export interface ConversationEvent extends SystemEvent {
    type: 'conversation.started' | 'conversation.ended' | 'message.sent' | 'message.received';
    data: {
        conversationId: string;
        message?: ChatMessage;
    };
}
export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
//# sourceMappingURL=index.d.ts.map