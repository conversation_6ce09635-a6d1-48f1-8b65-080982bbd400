import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { ProviderSelector } from './ProviderSelector';
import { ProviderConfigurator } from './ProviderConfigurator';
import { AuthMenu } from './AuthMenu';
import { CLIState, CLIConfig } from '../../types';

export class AuthManager extends BaseComponent {
  private configManager: ConfigManager;
  private providerSelector: ProviderSelector;
  private providerConfigurator: ProviderConfigurator;
  private authMenu: AuthMenu;

  constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager) {
    super(state, config);
    this.configManager = configManager;
    this.providerSelector = new ProviderSelector(state, config, configManager);
    this.providerConfigurator = new ProviderConfigurator(state, config, configManager);
    this.authMenu = new AuthMenu(state, config, configManager);
  }

  public async render(): Promise<void> {
    this.utils.clearScreen();
    this.utils.showBanner('🤖 AI CLI Terminal', 'Configure your AI providers to get started');

    const configuredProviders = this.configManager.getConfiguredProviders();

    if (configuredProviders.length === 0) {
      await this.setupFirstProvider();
    } else {
      await this.showMainMenu();
    }
  }

  private async setupFirstProvider(): Promise<void> {
    this.utils.showWarning('No AI providers configured. Let\'s set one up!');
    console.log();

    const selectedProvider = await this.providerSelector.selectProvider();
    if (selectedProvider) {
      await this.providerConfigurator.configureProvider(selectedProvider);
      
      // Mark as authenticated if configuration was successful
      if (this.configManager.isProviderConfigured(selectedProvider)) {
        this.updateState({ 
          isAuthenticated: true,
          currentProvider: selectedProvider,
          currentModel: this.configManager.getProviderConfig(selectedProvider).defaultModel
        });
      }
    }
  }

  private async showMainMenu(): Promise<void> {
    await this.authMenu.show();
    
    // Check if user chose to start terminal
    if (this.state.currentView === 'terminal') {
      const config = this.configManager.getConfig();
      this.updateState({
        isAuthenticated: true,
        currentProvider: config.defaultProvider,
        currentModel: this.configManager.getProviderConfig().defaultModel
      });
    }
  }

  public async handleProviderConfiguration(providerName: string): Promise<void> {
    await this.providerConfigurator.configureProvider(providerName);
  }

  public async handleProviderSelection(): Promise<string | null> {
    return await this.providerSelector.selectProvider();
  }

  public isAuthenticated(): boolean {
    return this.configManager.getConfiguredProviders().length > 0;
  }

  public getCurrentProvider(): string | null {
    const config = this.configManager.getConfig();
    return config.defaultProvider;
  }

  public async switchProvider(providerName: string): Promise<void> {
    if (!this.configManager.isProviderConfigured(providerName)) {
      throw new Error(`Provider ${providerName} is not configured`);
    }

    await this.configManager.setDefaultProvider(providerName);
    const providerConfig = this.configManager.getProviderConfig(providerName);
    
    this.updateState({
      currentProvider: providerName,
      currentModel: providerConfig.defaultModel
    });

    this.utils.showSuccess(`Switched to ${providerName}`);
  }
}
