import { BaseComponent } from '../common/BaseComponent';
import { Menu } from '../common/Menu';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { ProviderSelector } from './ProviderSelector';
import { ProviderConfigurator } from './ProviderConfigurator';
import { SUPPORTED_PROVIDERS } from '../../../core/types';
import { CLIState, CLIConfig, MenuOption } from '../../types';

export class AuthMenu extends BaseComponent {
  private configManager: ConfigManager;
  private providerSelector: ProviderSelector;
  private providerConfigurator: ProviderConfigurator;

  constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager) {
    super(state, config);
    this.configManager = configManager;
    this.providerSelector = new ProviderSelector(state, config, configManager);
    this.providerConfigurator = new ProviderConfigurator(state, config, configManager);
  }

  public async render(): Promise<void> {
    await this.show();
  }

  public async show(): Promise<void> {
    this.displayProviderStatus();
    
    const menu = new Menu(this.createMenuOptions(), this.config.theme, '🔧 Configuration Menu');
    await menu.show();
  }

  private displayProviderStatus(): void {
    const configuredProviders = this.configManager.getConfiguredProviders();
    const config = this.configManager.getConfig();
    
    console.log(this.utils.formatHeader('✅ Configured Providers'));
    
    if (configuredProviders.length === 0) {
      this.utils.showWarning('No providers configured');
    } else {
      configuredProviders.forEach(providerName => {
        const provider = SUPPORTED_PROVIDERS[providerName];
        const providerConfig = this.configManager.getProviderConfig(providerName);
        const isDefault = providerName === config.defaultProvider;
        
        const status = isDefault ? '●' : '○';
        const statusColor = isDefault ? this.config.theme.success : this.config.theme.muted;
        
        console.log(
          this.utils.colorize(`  ${status} ${provider.displayName} (${providerConfig.defaultModel})`, statusColor) +
          (isDefault ? this.utils.colorize(' - default', this.config.theme.muted) : '')
        );
      });
    }
    
    console.log();
  }

  private createMenuOptions(): MenuOption[] {
    const configuredProviders = this.configManager.getConfiguredProviders();
    const unconfiguredProviders = Object.keys(SUPPORTED_PROVIDERS).filter(
      p => !configuredProviders.includes(p)
    );

    const options: MenuOption[] = [
      {
        key: 'start',
        label: '🚀 Start Terminal',
        description: 'Begin AI conversation',
        action: async () => {
          if (configuredProviders.length === 0) {
            await this.showError('No providers configured. Please add a provider first.');
            await this.show();
          } else {
            this.updateState({ currentView: 'terminal' });
          }
        },
        disabled: configuredProviders.length === 0,
      },
    ];

    // Add provider management options
    if (unconfiguredProviders.length > 0) {
      options.push({
        key: 'add',
        label: '➕ Add Provider',
        description: 'Configure a new AI provider',
        action: async () => {
          const provider = await this.providerSelector.selectProvider(true);
          if (provider) {
            await this.providerConfigurator.configureProvider(provider);
          }
          await this.show();
        },
      });
    }

    if (configuredProviders.length > 0) {
      options.push(
        {
          key: 'configure',
          label: '⚙️ Configure Provider',
          description: 'Modify existing provider settings',
          action: async () => {
            const provider = await this.providerSelector.selectFromConfigured();
            if (provider) {
              await this.providerConfigurator.configureProvider(provider);
            }
            await this.show();
          },
        },
        {
          key: 'default',
          label: '🔄 Change Default Provider',
          description: 'Set which provider to use by default',
          action: async () => {
            await this.handleChangeDefault();
            await this.show();
          },
        },
        {
          key: 'test',
          label: '🧪 Test Provider',
          description: 'Test connection to a provider',
          action: async () => {
            await this.handleTestProvider();
            await this.show();
          },
        },
        {
          key: 'remove',
          label: '🗑️ Remove Provider',
          description: 'Remove a configured provider',
          action: async () => {
            await this.handleRemoveProvider();
            await this.show();
          },
        }
      );
    }

    // Add utility options
    options.push(
      {
        key: 'preferences',
        label: '⚙️ Preferences',
        description: 'Configure application preferences',
        action: async () => {
          await this.handlePreferences();
          await this.show();
        },
      },
      {
        key: 'export',
        label: '📤 Export Config',
        description: 'Export configuration to file',
        action: async () => {
          await this.handleExportConfig();
          await this.show();
        },
      },
      {
        key: 'import',
        label: '📥 Import Config',
        description: 'Import configuration from file',
        action: async () => {
          await this.handleImportConfig();
          await this.show();
        },
      },
      {
        key: 'reset',
        label: '🔄 Reset Configuration',
        description: 'Reset all settings to defaults',
        action: async () => {
          await this.handleResetConfig();
          await this.show();
        },
      },
      {
        key: 'exit',
        label: '❌ Exit',
        description: 'Exit the application',
        action: async () => {
          this.utils.showInfo('Goodbye! 👋');
          process.exit(0);
        },
      }
    );

    return options;
  }

  private async handleChangeDefault(): Promise<void> {
    const provider = await this.providerSelector.selectFromConfigured();
    if (provider) {
      await this.configManager.setDefaultProvider(provider);
      await this.showSuccess(`Default provider set to ${SUPPORTED_PROVIDERS[provider].displayName}`);
    }
  }

  private async handleTestProvider(): Promise<void> {
    const provider = await this.providerSelector.selectFromConfigured();
    if (provider) {
      await this.providerConfigurator.testProviderConnection(provider);
    }
  }

  private async handleRemoveProvider(): Promise<void> {
    const provider = await this.providerSelector.selectFromConfigured();
    if (!provider) return;

    const confirmed = await this.confirmAction(
      `Are you sure you want to remove ${SUPPORTED_PROVIDERS[provider].displayName}?`
    );

    if (confirmed) {
      const config = this.configManager.getConfig();
      delete config.providers[provider];
      
      // Set new default if removing current default
      if (config.defaultProvider === provider) {
        const remainingProviders = Object.keys(config.providers);
        if (remainingProviders.length > 0) {
          config.defaultProvider = remainingProviders[0];
        }
      }

      await this.configManager.saveConfig();
      await this.showSuccess(`${SUPPORTED_PROVIDERS[provider].displayName} removed successfully`);
    }
  }

  private async handlePreferences(): Promise<void> {
    // TODO: Implement preferences configuration
    this.utils.showInfo('Preferences configuration coming soon...');
    await this.utils.waitForKeyPress();
  }

  private async handleExportConfig(): Promise<void> {
    // TODO: Implement config export
    this.utils.showInfo('Config export coming soon...');
    await this.utils.waitForKeyPress();
  }

  private async handleImportConfig(): Promise<void> {
    // TODO: Implement config import
    this.utils.showInfo('Config import coming soon...');
    await this.utils.waitForKeyPress();
  }

  private async handleResetConfig(): Promise<void> {
    const confirmed = await this.confirmAction(
      'Are you sure you want to reset all configuration? This cannot be undone.'
    );

    if (confirmed) {
      await this.configManager.resetConfig();
      await this.showSuccess('Configuration reset successfully');
    }
  }
}
