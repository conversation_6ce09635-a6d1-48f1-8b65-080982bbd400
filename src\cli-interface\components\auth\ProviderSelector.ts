import inquirer from 'inquirer';
import { BaseComponent } from '../common/BaseComponent';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { SUPPORTED_PROVIDERS } from '../../../core/types';
import { CLIState, CLIConfig } from '../../types';

export class ProviderSelector extends BaseComponent {
  private configManager: ConfigManager;

  constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager) {
    super(state, config);
    this.configManager = configManager;
  }

  public async render(): Promise<void> {
    // This component is used inline, no standalone render needed
  }

  public async selectProvider(excludeConfigured: boolean = false): Promise<string | null> {
    const configuredProviders = this.configManager.getConfiguredProviders();
    
    let availableProviders = Object.values(SUPPORTED_PROVIDERS);
    
    if (excludeConfigured) {
      availableProviders = availableProviders.filter(
        provider => !configuredProviders.includes(provider.name)
      );
    }

    if (availableProviders.length === 0) {
      this.utils.showWarning('No providers available for selection');
      return null;
    }

    const choices = availableProviders.map(provider => ({
      name: this.formatProviderChoice(provider, configuredProviders.includes(provider.name)),
      value: provider.name,
    }));

    const { selectedProvider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedProvider',
        message: 'Choose an AI provider:',
        choices,
      },
    ]);

    return selectedProvider;
  }

  public async selectFromConfigured(): Promise<string | null> {
    const configuredProviders = this.configManager.getConfiguredProviders();
    
    if (configuredProviders.length === 0) {
      this.utils.showError('No providers are configured');
      return null;
    }

    const choices = configuredProviders.map(providerName => {
      const provider = SUPPORTED_PROVIDERS[providerName];
      const config = this.configManager.getProviderConfig(providerName);
      const isDefault = providerName === this.configManager.getConfig().defaultProvider;
      
      return {
        name: `${provider.displayName} (${config.defaultModel})${isDefault ? ' - default' : ''}`,
        value: providerName,
      };
    });

    const { selectedProvider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedProvider',
        message: 'Choose a provider:',
        choices,
      },
    ]);

    return selectedProvider;
  }

  public async selectModel(providerName: string): Promise<string | null> {
    const provider = SUPPORTED_PROVIDERS[providerName];
    if (!provider) {
      this.utils.showError(`Unknown provider: ${providerName}`);
      return null;
    }

    const currentConfig = this.configManager.getProviderConfig(providerName);
    
    const { selectedModel } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedModel',
        message: `Choose a model for ${provider.displayName}:`,
        choices: provider.models,
        default: currentConfig?.defaultModel || provider.models[0],
      },
    ]);

    return selectedModel;
  }

  private formatProviderChoice(provider: any, isConfigured: boolean): string {
    const status = isConfigured ? '✅' : '⚪';
    const models = provider.models.slice(0, 2).join(', ') + 
                  (provider.models.length > 2 ? '...' : '');
    
    return `${status} ${provider.displayName} (${models})`;
  }

  public async confirmProviderSwitch(fromProvider: string, toProvider: string): Promise<boolean> {
    const fromDisplay = SUPPORTED_PROVIDERS[fromProvider]?.displayName || fromProvider;
    const toDisplay = SUPPORTED_PROVIDERS[toProvider]?.displayName || toProvider;
    
    return await this.utils.confirmAction(
      `Switch from ${fromDisplay} to ${toDisplay}?`
    );
  }
}
