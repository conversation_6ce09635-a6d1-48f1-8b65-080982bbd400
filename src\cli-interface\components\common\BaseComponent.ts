import { <PERSON><PERSON><PERSON>omponent, CLIState, CLIConfig } from '../../types';
import { CLIUtils } from '../../utils/CLIUtils';

export abstract class BaseComponent implements CLIComponent {
  protected state: CLIState;
  protected config: CLIConfig;
  protected utils: CLIUtils;

  constructor(state: CLIState, config: CLIConfig) {
    this.state = state;
    this.config = config;
    this.utils = new CLIUtils(config.theme);
  }

  abstract render(): Promise<void>;

  public async handleInput(input: string): Promise<void> {
    // Default implementation - can be overridden
    console.log(`Received input: ${input}`);
  }

  public async cleanup(): Promise<void> {
    // Default cleanup - can be overridden
  }

  protected updateState(updates: Partial<CLIState>): void {
    Object.assign(this.state, updates);
  }

  protected async showError(message: string): Promise<void> {
    this.utils.showError(message);
    await this.utils.waitForKeyPress();
  }

  protected async showSuccess(message: string): Promise<void> {
    this.utils.showSuccess(message);
    await this.utils.waitForKeyPress();
  }

  protected async confirmAction(message: string): Promise<boolean> {
    return await this.utils.confirmAction(message);
  }
}
