import inquirer from 'inquirer';
import chalk from 'chalk';
import { MenuOption, CLITheme } from '../../types';

export class Menu {
  private options: MenuOption[];
  private theme: CLITheme;
  private title?: string;

  constructor(options: MenuOption[], theme: CLITheme, title?: string) {
    this.options = options;
    this.theme = theme;
    this.title = title;
  }

  public async show(): Promise<void> {
    if (this.title) {
      console.log(chalk[this.theme.primary as keyof typeof chalk](this.title));
      console.log();
    }

    const choices = this.options
      .filter(option => !option.disabled)
      .map(option => ({
        name: `${option.label}${option.description ? chalk[this.theme.muted as keyof typeof chalk](` - ${option.description}`) : ''}`,
        value: option.key,
      }));

    const { selectedKey } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedKey',
        message: 'Choose an option:',
        choices,
      },
    ]);

    const selectedOption = this.options.find(option => option.key === selectedKey);
    if (selectedOption) {
      await selectedOption.action();
    }
  }

  public addOption(option: MenuOption): void {
    this.options.push(option);
  }

  public removeOption(key: string): void {
    this.options = this.options.filter(option => option.key !== key);
  }

  public enableOption(key: string): void {
    const option = this.options.find(option => option.key === key);
    if (option) {
      option.disabled = false;
    }
  }

  public disableOption(key: string): void {
    const option = this.options.find(option => option.key === key);
    if (option) {
      option.disabled = true;
    }
  }
}
