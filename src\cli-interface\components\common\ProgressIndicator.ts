import ora, { Or<PERSON> } from 'ora';
import chalk from 'chalk';
import { ProgressIndicator, CLITheme } from '../../types';

export class CLIProgressIndicator implements ProgressIndicator {
  private spinner: Ora | null = null;
  private theme: CLITheme;

  constructor(theme: CLITheme) {
    this.theme = theme;
  }

  public start(message: string): void {
    if (this.spinner) {
      this.spinner.stop();
    }
    this.spinner = ora({
      text: message,
      color: this.theme.primary as any,
    }).start();
  }

  public update(message: string): void {
    if (this.spinner) {
      this.spinner.text = message;
    }
  }

  public succeed(message?: string): void {
    if (this.spinner) {
      this.spinner.succeed(message);
      this.spinner = null;
    }
  }

  public fail(message?: string): void {
    if (this.spinner) {
      this.spinner.fail(message);
      this.spinner = null;
    }
  }

  public stop(): void {
    if (this.spinner) {
      this.spinner.stop();
      this.spinner = null;
    }
  }
}

export class SimpleProgressIndicator implements ProgressIndicator {
  private theme: CLITheme;

  constructor(theme: CLITheme) {
    this.theme = theme;
  }

  public start(message: string): void {
    console.log(chalk[this.theme.info as keyof typeof chalk](`⏳ ${message}`));
  }

  public update(message: string): void {
    console.log(chalk[this.theme.info as keyof typeof chalk](`⏳ ${message}`));
  }

  public succeed(message?: string): void {
    if (message) {
      console.log(chalk[this.theme.success as keyof typeof chalk](`✅ ${message}`));
    }
  }

  public fail(message?: string): void {
    if (message) {
      console.log(chalk[this.theme.error as keyof typeof chalk](`❌ ${message}`));
    }
  }

  public stop(): void {
    // No-op for simple indicator
  }
}
