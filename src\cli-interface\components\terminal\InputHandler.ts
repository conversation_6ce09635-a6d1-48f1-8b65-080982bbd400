import inquirer from 'inquirer';
import { BaseComponent } from '../common/BaseComponent';
import { CLIState, CLIConfig, InputHandler as IInputHandler } from '../../types';

export class InputHandler extends BaseComponent {
  private inputHandlers: IInputHandler[] = [];

  constructor(state: CLIState, config: CLIConfig) {
    super(state, config);
    this.setupDefaultHandlers();
  }

  public async render(): Promise<void> {
    // This component is used inline, no standalone render needed
  }

  public async getInput(): Promise<string> {
    const prompt = this.createPrompt();
    
    const { input } = await inquirer.prompt([
      {
        type: 'input',
        name: 'input',
        message: prompt,
        validate: this.validateInput.bind(this),
      },
    ]);

    return input.trim();
  }

  public async getMultilineInput(): Promise<string> {
    console.log(this.utils.colorize('Enter your message (type "END" on a new line to finish):', this.config.theme.info));
    
    const lines: string[] = [];
    
    while (true) {
      const { line } = await inquirer.prompt([
        {
          type: 'input',
          name: 'line',
          message: this.utils.colorize('│', this.config.theme.muted),
        },
      ]);

      if (line.trim() === 'END') {
        break;
      }

      lines.push(line);
    }

    return lines.join('\n').trim();
  }

  public async getConfirmation(message: string, defaultValue: boolean = false): Promise<boolean> {
    return await this.utils.confirmAction(message, defaultValue);
  }

  public async selectFromOptions<T>(
    message: string,
    options: Array<{ name: string; value: T }>,
    defaultValue?: T
  ): Promise<T> {
    return await this.utils.selectFromList(message, options, defaultValue);
  }

  public addInputHandler(handler: IInputHandler): void {
    this.inputHandlers.push(handler);
  }

  public removeInputHandler(pattern: RegExp): void {
    this.inputHandlers = this.inputHandlers.filter(h => h.pattern.source !== pattern.source);
  }

  private setupDefaultHandlers(): void {
    // Handler for multiline input trigger
    this.addInputHandler({
      pattern: /^\/multiline$/i,
      handler: async () => {
        const input = await this.getMultilineInput();
        return input;
      },
      description: 'Enter multiline input mode',
    });

    // Handler for file input
    this.addInputHandler({
      pattern: /^\/file\s+(.+)$/i,
      handler: async (input, matches) => {
        const filePath = matches[1];
        return await this.handleFileInput(filePath);
      },
      description: 'Read content from a file',
    });

    // Handler for clipboard paste (if available)
    this.addInputHandler({
      pattern: /^\/paste$/i,
      handler: async () => {
        return await this.handleClipboardPaste();
      },
      description: 'Paste from clipboard',
    });
  }

  private createPrompt(): string {
    const promptSymbol = this.utils.colorize('❯', this.config.theme.primary);
    return promptSymbol;
  }

  private validateInput(input: string): boolean | string {
    if (!input || input.trim().length === 0) {
      return 'Please enter a message or command';
    }

    // Check for special input handlers
    for (const handler of this.inputHandlers) {
      if (handler.pattern.test(input)) {
        return true; // Will be handled by the handler
      }
    }

    return true;
  }

  private async handleFileInput(filePath: string): Promise<string> {
    try {
      const fs = await import('fs-extra');
      const path = await import('path');
      
      const resolvedPath = path.resolve(filePath);
      
      if (!await fs.pathExists(resolvedPath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      const content = await fs.readFile(resolvedPath, 'utf-8');
      this.utils.showSuccess(`Loaded content from ${filePath} (${this.utils.formatFileSize(content.length)} characters)`);
      
      return content;
    } catch (error) {
      this.utils.showError(`Failed to read file: ${error}`);
      return '';
    }
  }

  private async handleClipboardPaste(): Promise<string> {
    try {
      // This would require a clipboard library like 'clipboardy'
      // For now, just show a message
      this.utils.showInfo('Clipboard paste not implemented yet. Please type your content manually.');
      return await this.getMultilineInput();
    } catch (error) {
      this.utils.showError(`Failed to access clipboard: ${error}`);
      return '';
    }
  }

  public async handleSpecialInput(input: string): Promise<string | null> {
    for (const handler of this.inputHandlers) {
      const matches = input.match(handler.pattern);
      if (matches) {
        try {
          const result = await handler.handler(input, matches);
          return result || null;
        } catch (error) {
          this.utils.showError(`Handler error: ${error}`);
          return null;
        }
      }
    }
    return null;
  }

  public showInputHelp(): void {
    console.log(this.utils.formatHeader('📝 Input Help'));
    console.log();
    console.log('Special input commands:');
    
    for (const handler of this.inputHandlers) {
      console.log(`  ${this.utils.colorize(handler.pattern.source, this.config.theme.accent)} - ${handler.description}`);
    }
    
    console.log();
    console.log('General commands:');
    console.log(`  ${this.utils.colorize('/help', this.config.theme.accent)} - Show available commands`);
    console.log(`  ${this.utils.colorize('/multiline', this.config.theme.accent)} - Enter multiline input mode`);
    console.log(`  ${this.utils.colorize('/file <path>', this.config.theme.accent)} - Load content from file`);
    console.log(`  ${this.utils.colorize('/paste', this.config.theme.accent)} - Paste from clipboard`);
    console.log();
  }
}
