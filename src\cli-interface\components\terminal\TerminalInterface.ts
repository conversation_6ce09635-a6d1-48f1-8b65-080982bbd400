import { BaseComponent } from '../common/BaseComponent';
import { CLIState, CLIConfig } from '../../types';

export class TerminalInterface extends BaseComponent {
  constructor(state: CLIState, config: CLIConfig) {
    super(state, config);
  }

  public async render(): Promise<void> {
    this.utils.clearScreen();
    this.renderHeader();
    this.renderStatusBar();
    console.log();
  }

  private renderHeader(): void {
    const title = '🤖 AI CLI Terminal';
    const subtitle = 'Interactive AI Assistant';
    
    this.utils.showBanner(title, subtitle);
  }

  private renderStatusBar(): void {
    const statusItems = [
      `Provider: ${this.utils.colorize(this.state.currentProvider, this.config.theme.accent)}`,
      `Model: ${this.utils.colorize(this.state.currentModel, this.config.theme.accent)}`,
      `Status: ${this.utils.colorize('Connected', this.config.theme.success)}`,
    ];

    const statusBar = '│ ' + statusItems.join(' │ ') + ' │';
    const separator = '─'.repeat(statusBar.length - 2);
    
    console.log(this.utils.colorize('┌' + separator + '┐', this.config.theme.muted));
    console.log(this.utils.colorize(statusBar, this.config.theme.muted));
    console.log(this.utils.colorize('└' + separator + '┘', this.config.theme.muted));
  }

  public renderPrompt(): string {
    const promptSymbol = this.utils.colorize('❯', this.config.theme.primary);
    return `${promptSymbol} `;
  }

  public renderTypingIndicator(): void {
    this.utils.startSpinner('AI is thinking...');
  }

  public stopTypingIndicator(): void {
    this.utils.stopSpinner();
  }

  public renderError(error: string): void {
    console.log();
    this.utils.showError(error);
    console.log();
  }

  public renderWarning(warning: string): void {
    console.log();
    this.utils.showWarning(warning);
    console.log();
  }

  public renderInfo(info: string): void {
    console.log();
    this.utils.showInfo(info);
    console.log();
  }

  public renderSeparator(): void {
    this.utils.printSeparator('─', 60);
  }

  public async renderToolConfirmation(toolName: string, parameters: any): Promise<boolean> {
    console.log();
    console.log(this.utils.colorize('🔧 Tool Execution Request', this.config.theme.warning, true));
    console.log();
    console.log(`Tool: ${this.utils.colorize(toolName, this.config.theme.accent)}`);
    console.log('Parameters:');
    
    for (const [key, value] of Object.entries(parameters)) {
      const formattedValue = typeof value === 'string' && value.length > 100 
        ? this.utils.truncateText(value, 100)
        : JSON.stringify(value);
      
      console.log(`  ${this.utils.colorize(key, this.config.theme.secondary)}: ${formattedValue}`);
    }
    
    console.log();
    return await this.utils.confirmAction('Allow this tool execution?', false);
  }

  public renderToolResult(toolName: string, result: any, duration?: number): void {
    console.log();
    console.log(this.utils.colorize(`🔧 Tool Result: ${toolName}`, this.config.theme.success));
    
    if (duration) {
      console.log(this.utils.colorize(`⏱️  Duration: ${this.utils.formatDuration(duration)}`, this.config.theme.muted));
    }
    
    if (result.error) {
      console.log(this.utils.colorize(`❌ Error: ${result.error}`, this.config.theme.error));
    } else if (result.content) {
      console.log();
      console.log(result.content);
    }
    
    console.log();
  }

  public async renderStreamingResponse(content: string): Promise<void> {
    // For now, just print the content
    // In a full implementation, this would handle character-by-character streaming
    process.stdout.write(content);
  }

  public renderMessageTimestamp(timestamp: Date): string {
    if (!this.config.showTimestamps) {
      return '';
    }
    
    const timeStr = timestamp.toLocaleTimeString();
    return this.utils.colorize(`[${timeStr}]`, this.config.theme.muted) + ' ';
  }

  public renderTokenCount(tokens?: number): string {
    if (!this.config.showTokenCounts || !tokens) {
      return '';
    }
    
    return this.utils.colorize(`(${tokens} tokens)`, this.config.theme.muted) + ' ';
  }
}
