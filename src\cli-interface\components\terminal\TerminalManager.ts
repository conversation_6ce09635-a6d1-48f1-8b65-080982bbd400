import { BaseComponent } from '../common/BaseComponent';
import { TerminalInterface } from './TerminalInterface';
import { MessageRenderer } from './MessageRenderer';
import { InputHandler } from './InputHandler';
import { ToolConfirmationHandler } from './ToolConfirmationHandler';
import { ConfigManager } from '../../../core/config/ConfigManager';
import { CLIState, CLIConfig, TerminalSession, TerminalMessage } from '../../types';
import { v4 as uuidv4 } from 'uuid';

export class TerminalManager extends BaseComponent {
  private configManager: ConfigManager;
  private terminalInterface: TerminalInterface;
  private messageRenderer: MessageRenderer;
  private inputHandler: InputHandler;
  private toolConfirmationHandler: ToolConfirmationHandler;
  private currentSession: TerminalSession | null = null;

  constructor(state: CLIState, config: CLIConfig, configManager: ConfigManager) {
    super(state, config);
    this.configManager = configManager;
    this.terminalInterface = new TerminalInterface(state, config);
    this.messageRenderer = new MessageRenderer(state, config);
    this.inputHandler = new InputHandler(state, config);
    this.toolConfirmationHandler = new ToolConfirmationHandler(state, config);
  }

  public async render(): Promise<void> {
    await this.initializeSession();
    await this.terminalInterface.render();
    await this.startConversationLoop();
  }

  private async initializeSession(): Promise<void> {
    this.currentSession = {
      id: uuidv4(),
      startTime: new Date(),
      messages: [],
      provider: this.state.currentProvider,
      model: this.state.currentModel,
    };

    // Add welcome message
    const welcomeMessage: TerminalMessage = {
      id: uuidv4(),
      role: 'system',
      content: this.createWelcomeMessage(),
      timestamp: new Date(),
    };

    this.currentSession.messages.push(welcomeMessage);
    await this.messageRenderer.renderMessage(welcomeMessage);
  }

  private createWelcomeMessage(): string {
    const config = this.configManager.getConfig();
    const providerConfig = this.configManager.getProviderConfig();
    
    return `Welcome to AI CLI Terminal!

🤖 Provider: ${this.state.currentProvider}
📋 Model: ${this.state.currentModel}
🔧 Tools: ${this.getEnabledToolsCount()} enabled

Type your message or use these commands:
• /help - Show available commands
• /history - View conversation history
• /clear - Clear current conversation
• /switch - Switch AI provider
• /config - Open configuration
• /exit - Exit terminal

Ready to assist you!`;
  }

  private getEnabledToolsCount(): number {
    const prefs = this.configManager.getConfig().preferences;
    let count = 0;
    if (prefs.enableFileTools) count += 7; // File system tools
    if (prefs.enableShellTools) count += 1; // Shell tool
    if (prefs.enableWebTools) count += 2; // Web tools
    if (prefs.enableMemoryTools) count += 1; // Memory tool
    return count;
  }

  private async startConversationLoop(): Promise<void> {
    while (true) {
      try {
        const userInput = await this.inputHandler.getInput();
        
        if (await this.handleCommand(userInput)) {
          continue; // Command was handled, continue loop
        }

        // Process as regular message
        await this.processUserMessage(userInput);
        
      } catch (error) {
        if (error instanceof Error && error.message === 'EXIT_TERMINAL') {
          break;
        }
        
        this.utils.showError(`Error: ${error}`);
        await this.utils.waitForKeyPress();
      }
    }
  }

  private async handleCommand(input: string): Promise<boolean> {
    if (!input.startsWith('/')) {
      return false;
    }

    const [command, ...args] = input.slice(1).split(' ');
    
    switch (command.toLowerCase()) {
      case 'help':
        await this.showHelp();
        return true;
        
      case 'history':
        await this.showHistory();
        return true;
        
      case 'clear':
        await this.clearConversation();
        return true;
        
      case 'switch':
        await this.switchProvider();
        return true;
        
      case 'config':
        this.updateState({ currentView: 'config' });
        throw new Error('EXIT_TERMINAL');
        
      case 'exit':
        await this.exitTerminal();
        throw new Error('EXIT_TERMINAL');
        
      default:
        this.utils.showError(`Unknown command: /${command}`);
        await this.utils.waitForKeyPress();
        return true;
    }
  }

  private async processUserMessage(input: string): Promise<void> {
    const userMessage: TerminalMessage = {
      id: uuidv4(),
      role: 'user',
      content: input,
      timestamp: new Date(),
    };

    this.currentSession!.messages.push(userMessage);
    await this.messageRenderer.renderMessage(userMessage);

    // TODO: Send to AI provider and handle response
    this.utils.showInfo('AI response processing will be implemented in the next phase...');
    await this.utils.waitForKeyPress();
  }

  private async showHelp(): Promise<void> {
    const helpText = `
🔧 Available Commands:

/help     - Show this help message
/history  - View conversation history
/clear    - Clear current conversation
/switch   - Switch AI provider
/config   - Open configuration menu
/exit     - Exit terminal

🛠️ Tool Features:
• File operations (read, write, edit, search)
• Shell command execution
• Web content fetching and search
• Persistent memory management
• Model Context Protocol (MCP) support

💡 Tips:
• Use natural language to describe what you want to do
• The AI can use tools to help with file operations, web searches, etc.
• Tool execution requires confirmation unless disabled in preferences
`;

    console.log(this.utils.colorize(helpText, this.config.theme.info));
    await this.utils.waitForKeyPress();
  }

  private async showHistory(): Promise<void> {
    if (!this.currentSession || this.currentSession.messages.length <= 1) {
      this.utils.showInfo('No conversation history yet.');
      await this.utils.waitForKeyPress();
      return;
    }

    console.log(this.utils.formatHeader('📜 Conversation History'));
    console.log();

    for (const message of this.currentSession.messages) {
      if (message.role !== 'system') {
        await this.messageRenderer.renderMessage(message, true);
      }
    }

    await this.utils.waitForKeyPress();
  }

  private async clearConversation(): Promise<void> {
    const confirmed = await this.confirmAction('Clear current conversation?');
    if (confirmed) {
      await this.initializeSession();
      this.utils.showSuccess('Conversation cleared');
      await this.utils.waitForKeyPress();
    }
  }

  private async switchProvider(): Promise<void> {
    this.updateState({ currentView: 'auth' });
    throw new Error('EXIT_TERMINAL');
  }

  private async exitTerminal(): Promise<void> {
    // Save session to history
    if (this.currentSession && this.currentSession.messages.length > 1) {
      await this.configManager.addToHistory(this.currentSession);
    }

    this.utils.showInfo('Session saved. Goodbye! 👋');
    process.exit(0);
  }

  public getCurrentSession(): TerminalSession | null {
    return this.currentSession;
  }

  public async addMessage(message: TerminalMessage): Promise<void> {
    if (this.currentSession) {
      this.currentSession.messages.push(message);
      await this.messageRenderer.renderMessage(message);
    }
  }

  public async confirmToolExecution(toolName: string, parameters: Record<string, any>): Promise<boolean> {
    return await this.toolConfirmationHandler.confirmToolExecution(toolName, parameters);
  }

  public async startNewConversation(): Promise<void> {
    this.currentSession = {
      id: uuidv4(),
      startTime: new Date(),
      messages: [],
      provider: this.state.currentProvider,
      model: this.state.currentModel,
    };

    // Add welcome message
    const welcomeMessage: TerminalMessage = {
      id: uuidv4(),
      role: 'system',
      content: this.createWelcomeMessage(),
      timestamp: new Date(),
    };

    this.currentSession.messages.push(welcomeMessage);
    await this.messageRenderer.renderMessage(welcomeMessage);
  }

  public async loadConversation(conversationId: string): Promise<void> {
    // In a real implementation, this would load the conversation from storage
    // For now, just start a new conversation
    await this.startNewConversation();
    this.updateState({ conversationId });
  }
}
