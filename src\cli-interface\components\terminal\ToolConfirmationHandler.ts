import { BaseComponent } from '../common/BaseComponent';
import { CLIState, CLIConfig, ToolCallDisplay } from '../../types';

export class ToolConfirmationHandler extends BaseComponent {
  private trustedTools: Set<string> = new Set();
  private deniedTools: Set<string> = new Set();

  constructor(state: CLIState, config: CLIConfig) {
    super(state, config);
    this.setupTrustedTools();
  }

  public async render(): Promise<void> {
    // This component is used inline, no standalone render needed
  }

  public async confirmExecution(
    toolName: string,
    parameters: Record<string, any>
  ): Promise<boolean> {
    return this.confirmToolExecution(toolName, parameters);
  }

  public async confirmToolExecution(
    toolName: string,
    parameters: Record<string, any>,
    description?: string
  ): Promise<boolean> {
    // Check if tool confirmation is disabled
    // TODO: Get this from config manager
    const requireConfirmation = true; // this.configManager.getConfig().preferences.requireToolConfirmation;
    
    if (!requireConfirmation) {
      return true;
    }

    // Check if tool is in trusted list
    if (this.trustedTools.has(toolName)) {
      return true;
    }

    // Check if tool is in denied list
    if (this.deniedTools.has(toolName)) {
      return false;
    }

    return await this.showConfirmationDialog(toolName, parameters, description);
  }

  private async showConfirmationDialog(
    toolName: string,
    parameters: Record<string, any>,
    description?: string
  ): Promise<boolean> {
    console.log();
    console.log(this.utils.colorize('🔧 Tool Execution Request', this.config.theme.warning, true));
    this.utils.printSeparator('─', 50);
    
    console.log(`Tool: ${this.utils.colorize(toolName, this.config.theme.accent, true)}`);
    
    if (description) {
      console.log(`Description: ${this.utils.colorize(description, this.config.theme.info)}`);
    }

    console.log();
    console.log(this.utils.colorize('Parameters:', this.config.theme.secondary, true));
    
    await this.renderParameters(parameters);
    
    console.log();
    this.utils.printSeparator('─', 50);

    const choices = [
      { name: '✅ Allow once', value: 'allow' },
      { name: '🔒 Allow and trust this tool', value: 'trust' },
      { name: '❌ Deny once', value: 'deny' },
      { name: '🚫 Deny and block this tool', value: 'block' },
      { name: '📋 Show tool details', value: 'details' },
    ];

    const { action } = await this.utils.selectFromList(
      'What would you like to do?',
      choices
    );

    switch (action) {
      case 'allow':
        return true;
        
      case 'trust':
        this.trustedTools.add(toolName);
        this.utils.showSuccess(`Tool "${toolName}" added to trusted list`);
        return true;
        
      case 'deny':
        return false;
        
      case 'block':
        this.deniedTools.add(toolName);
        this.utils.showWarning(`Tool "${toolName}" added to blocked list`);
        return false;
        
      case 'details':
        await this.showToolDetails(toolName, parameters);
        return await this.showConfirmationDialog(toolName, parameters, description);
        
      default:
        return false;
    }
  }

  private async renderParameters(parameters: Record<string, any>): Promise<void> {
    for (const [key, value] of Object.entries(parameters)) {
      const formattedKey = this.utils.colorize(`  ${key}:`, this.config.theme.secondary);
      const formattedValue = this.formatParameterValue(value);
      
      console.log(`${formattedKey} ${formattedValue}`);
    }
  }

  private formatParameterValue(value: any): string {
    if (typeof value === 'string') {
      if (value.length > 100) {
        const truncated = this.utils.truncateText(value, 100);
        return this.utils.colorize(truncated, this.config.theme.muted);
      }
      return this.utils.colorize(`"${value}"`, this.config.theme.accent);
    }
    
    if (typeof value === 'object') {
      const jsonStr = JSON.stringify(value, null, 2);
      if (jsonStr.length > 200) {
        return this.utils.colorize('[Complex Object - see details]', this.config.theme.muted);
      }
      return this.utils.colorize(jsonStr, this.config.theme.accent);
    }
    
    return this.utils.colorize(String(value), this.config.theme.accent);
  }

  private async showToolDetails(toolName: string, parameters: Record<string, any>): Promise<void> {
    console.log();
    console.log(this.utils.colorize(`🔍 Tool Details: ${toolName}`, this.config.theme.primary, true));
    this.utils.printSeparator('─', 50);
    
    // Show full parameters
    console.log(this.utils.colorize('Full Parameters:', this.config.theme.secondary, true));
    for (const [key, value] of Object.entries(parameters)) {
      console.log(`${this.utils.colorize(key, this.config.theme.secondary)}:`);
      
      if (typeof value === 'string') {
        console.log(this.utils.formatCodeBlock(value, 'text'));
      } else {
        console.log(this.utils.formatCodeBlock(JSON.stringify(value, null, 2), 'json'));
      }
    }
    
    // Show tool safety information
    console.log(this.utils.colorize('Safety Information:', this.config.theme.warning, true));
    const safetyInfo = this.getToolSafetyInfo(toolName);
    console.log(this.utils.colorize(safetyInfo, this.config.theme.info));
    
    await this.utils.waitForKeyPress();
  }

  private getToolSafetyInfo(toolName: string): string {
    const safetyInfoMap: Record<string, string> = {
      'write_file': 'This tool can create or overwrite files on your system. Review the file path and content carefully.',
      'run_shell_command': 'This tool executes shell commands. Only allow if you trust the command being executed.',
      'read_file': 'This tool reads file contents. Generally safe but may expose sensitive information.',
      'web_fetch': 'This tool fetches content from the internet. Generally safe but may access external resources.',
      'google_web_search': 'This tool performs web searches. Generally safe.',
      'save_memory': 'This tool saves information to persistent memory. Review what information is being stored.',
      'list_directory': 'This tool lists directory contents. Generally safe.',
      'search_file_content': 'This tool searches for patterns in files. Generally safe.',
      'glob': 'This tool finds files matching patterns. Generally safe.',
      'replace': 'This tool modifies file contents. Review the changes carefully.',
      'read_many_files': 'This tool reads multiple files. Generally safe but may access many files.',
    };

    return safetyInfoMap[toolName] || 'No specific safety information available for this tool. Use caution.';
  }

  public getTrustedTools(): string[] {
    return Array.from(this.trustedTools);
  }

  public getDeniedTools(): string[] {
    return Array.from(this.deniedTools);
  }

  public trustTool(toolName: string): void {
    this.trustedTools.add(toolName);
    this.deniedTools.delete(toolName);
  }

  public denyTool(toolName: string): void {
    this.deniedTools.add(toolName);
    this.trustedTools.delete(toolName);
  }

  public resetTool(toolName: string): void {
    this.trustedTools.delete(toolName);
    this.deniedTools.delete(toolName);
  }

  public clearAllTrusted(): void {
    this.trustedTools.clear();
  }

  public clearAllDenied(): void {
    this.deniedTools.clear();
  }

  private setupTrustedTools(): void {
    // Add some generally safe tools to trusted list by default
    const defaultTrustedTools = [
      'read_file',
      'list_directory',
      'search_file_content',
      'glob',
      'web_fetch',
      'google_web_search',
    ];

    // Only add to trusted if user hasn't configured preferences yet
    // TODO: Check if this is first run
    const isFirstRun = true;
    
    if (isFirstRun) {
      defaultTrustedTools.forEach(tool => this.trustedTools.add(tool));
    }
  }

  public async showToolManagement(): Promise<void> {
    console.log(this.utils.formatHeader('🔧 Tool Management'));
    
    console.log(this.utils.colorize('Trusted Tools:', this.config.theme.success, true));
    if (this.trustedTools.size === 0) {
      console.log(this.utils.colorize('  None', this.config.theme.muted));
    } else {
      this.trustedTools.forEach(tool => {
        console.log(`  ${this.utils.colorize('✅', this.config.theme.success)} ${tool}`);
      });
    }

    console.log();
    console.log(this.utils.colorize('Blocked Tools:', this.config.theme.error, true));
    if (this.deniedTools.size === 0) {
      console.log(this.utils.colorize('  None', this.config.theme.muted));
    } else {
      this.deniedTools.forEach(tool => {
        console.log(`  ${this.utils.colorize('❌', this.config.theme.error)} ${tool}`);
      });
    }

    await this.utils.waitForKeyPress();
  }
}
