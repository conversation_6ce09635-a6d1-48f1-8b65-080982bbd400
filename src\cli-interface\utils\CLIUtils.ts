import chalk from 'chalk';
import boxen from 'boxen';
import ora, { Ora } from 'ora';
import inquirer from 'inquirer';
import { CLITheme, DEFAULT_CLI_THEME } from '../types';

export class CLIUtils {
  private static spinner: Ora | null = null;
  private theme: CLITheme;

  constructor(theme: CLITheme = DEFAULT_CLI_THEME) {
    this.theme = theme;
  }

  public showBanner(title: string, subtitle?: string): void {
    const content = this.colorize(title, this.theme.primary, true) + 
                   (subtitle ? '\n' + this.colorize(subtitle, this.theme.muted) : '');
    
    const banner = boxen(content, {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: this.theme.primary as any,
      textAlignment: 'center'
    });
    
    console.log(banner);
  }

  public showSuccess(message: string): void {
    console.log(this.colorize('✅ ' + message, this.theme.success));
  }

  public showError(message: string): void {
    console.log(this.colorize('❌ ' + message, this.theme.error));
  }

  public showWarning(message: string): void {
    console.log(this.colorize('⚠️  ' + message, this.theme.warning));
  }

  public showInfo(message: string): void {
    console.log(this.colorize('ℹ️  ' + message, this.theme.info));
  }

  public showDebug(message: string): void {
    console.log(this.colorize('🐛 ' + message, this.theme.muted));
  }

  public colorize(text: string, color: string, bold: boolean = false): string {
    let colorFunc = chalk[color as keyof typeof chalk] as any;
    if (bold) {
      colorFunc = colorFunc.bold;
    }
    return colorFunc(text);
  }

  public formatHeader(text: string): string {
    return this.colorize(text, this.theme.primary, true);
  }

  public startSpinner(text: string): void {
    if (CLIUtils.spinner) {
      CLIUtils.spinner.stop();
    }
    CLIUtils.spinner = ora({
      text,
      color: this.theme.primary as any,
    }).start();
  }

  public updateSpinner(text: string): void {
    if (CLIUtils.spinner) {
      CLIUtils.spinner.text = text;
    }
  }

  public stopSpinner(success: boolean = true, message?: string): void {
    if (CLIUtils.spinner) {
      if (success) {
        CLIUtils.spinner.succeed(message);
      } else {
        CLIUtils.spinner.fail(message);
      }
      CLIUtils.spinner = null;
    }
  }

  public async confirmAction(message: string, defaultValue: boolean = false): Promise<boolean> {
    const { confirmed } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirmed',
        message,
        default: defaultValue,
      },
    ]);
    return confirmed;
  }

  public async selectFromList<T>(
    message: string,
    choices: Array<{ name: string; value: T }>,
    defaultValue?: T
  ): Promise<T> {
    const { selected } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selected',
        message,
        choices,
        default: defaultValue,
      },
    ]);
    return selected;
  }

  public async getInput(
    message: string,
    defaultValue?: string,
    validate?: (input: string) => boolean | string
  ): Promise<string> {
    const { input } = await inquirer.prompt([
      {
        type: 'input',
        name: 'input',
        message,
        default: defaultValue,
        validate,
      },
    ]);
    return input;
  }

  public async getPassword(message: string): Promise<string> {
    const { password } = await inquirer.prompt([
      {
        type: 'password',
        name: 'password',
        message,
        validate: (input: string) => input.trim().length > 0 || 'Password is required',
      },
    ]);
    return password;
  }

  public formatCodeBlock(code: string, language: string = ''): string {
    const lines = code.split('\n');
    const formattedLines = lines.map((line, index) => {
      const lineNumber = (index + 1).toString().padStart(3, ' ');
      return this.colorize(`${lineNumber} │ `, this.theme.muted) + line;
    });
    
    return boxen(
      this.colorize(`${language}\n`, this.theme.accent) + formattedLines.join('\n'),
      {
        padding: 1,
        borderStyle: 'round',
        borderColor: this.theme.muted as any,
      }
    );
  }

  public formatDiff(oldContent: string, newContent: string): string {
    const oldLines = oldContent.split('\n');
    const newLines = newContent.split('\n');
    
    const maxLines = Math.max(oldLines.length, newLines.length);
    const diffLines: string[] = [];
    
    for (let i = 0; i < maxLines; i++) {
      const oldLine = oldLines[i] || '';
      const newLine = newLines[i] || '';
      
      if (oldLine !== newLine) {
        if (oldLine) {
          diffLines.push(this.colorize(`- ${oldLine}`, this.theme.error));
        }
        if (newLine) {
          diffLines.push(this.colorize(`+ ${newLine}`, this.theme.success));
        }
      } else if (oldLine) {
        diffLines.push(this.colorize(`  ${oldLine}`, this.theme.muted));
      }
    }
    
    return boxen(
      this.colorize('Diff Preview:\n', this.theme.warning, true) + diffLines.join('\n'),
      {
        padding: 1,
        borderStyle: 'round',
        borderColor: this.theme.warning as any,
      }
    );
  }

  public async waitForKeyPress(message: string = 'Press Enter to continue...'): Promise<void> {
    await inquirer.prompt([
      {
        type: 'input',
        name: 'continue',
        message,
      },
    ]);
  }

  public clearScreen(): void {
    console.clear();
  }

  public printSeparator(char: string = '─', length: number = 50): void {
    console.log(this.colorize(char.repeat(length), this.theme.muted));
  }

  public formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  public formatDuration(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  }

  public truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  }
}
