import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';
import {
  AppConfig,
  ProviderConfiguration,
  DEFAULT_APP_CONFIG,
  SupportedProvider,
  SUPPORTED_PROVIDERS,
  SUPPORTED_PROVIDER_NAMES,
  DEFAULT_PROVIDER_MODELS,
  ConfigError
} from '../types';
import { ProviderConfig } from '../providers/BaseProvider';

export class ConfigManager {
  private config: AppConfig;
  private configPath: string;
  private configDir: string;

  constructor(configDir?: string) {
    this.configDir = configDir || this.expandPath(DEFAULT_APP_CONFIG.configDir);
    this.configPath = path.join(this.configDir, 'config.json');
    this.config = { ...DEFAULT_APP_CONFIG };
    this.loadConfig();
  }

  public async initialize(): Promise<void> {
    await this.loadConfig();
  }

  private expandPath(filePath: string): string {
    if (filePath.startsWith('~')) {
      return path.join(os.homedir(), filePath.slice(1));
    }
    return filePath;
  }

  private async ensureConfigDir(): Promise<void> {
    try {
      await fs.ensureDir(this.configDir);
    } catch (error) {
      throw new ConfigError(`Failed to create config directory: ${this.configDir}`, { error });
    }
  }

  public async loadConfig(): Promise<void> {
    try {
      if (await fs.pathExists(this.configPath)) {
        const configData = await fs.readJson(this.configPath);
        this.config = this.mergeConfig(DEFAULT_APP_CONFIG, configData);
      } else {
        // Create default config file
        await this.saveConfig();
      }
    } catch (error) {
      console.warn(`Failed to load config from ${this.configPath}, using defaults:`, error);
      this.config = { ...DEFAULT_APP_CONFIG };
    }
  }

  public async saveConfig(): Promise<void> {
    try {
      await this.ensureConfigDir();
      await fs.writeJson(this.configPath, this.config, { spaces: 2 });
    } catch (error) {
      throw new ConfigError(`Failed to save config to ${this.configPath}`, { error });
    }
  }

  private mergeConfig(defaultConfig: AppConfig, userConfig: any): AppConfig {
    return {
      ...defaultConfig,
      ...userConfig,
      providers: {
        ...defaultConfig.providers,
        ...userConfig.providers
      },
      tools: {
        ...defaultConfig.tools,
        ...userConfig.tools
      },
      security: {
        ...defaultConfig.security,
        ...userConfig.security
      },
      ui: {
        ...defaultConfig.ui,
        ...userConfig.ui
      }
    };
  }

  public getConfig(): AppConfig {
    return { ...this.config };
  }

  public async updateConfig(updates: Partial<AppConfig>): Promise<void> {
    this.config = this.mergeConfig(this.config, updates);
    await this.saveConfig();
  }

  // Provider management
  public async configureProvider(
    providerName: SupportedProvider, 
    config: Partial<ProviderConfiguration>
  ): Promise<void> {
    if (!SUPPORTED_PROVIDER_NAMES.includes(providerName)) {
      throw new ConfigError(`Unsupported provider: ${providerName}`);
    }

    const providerConfig: ProviderConfiguration = {
      apiKey: config.apiKey || '',
      baseUrl: config.baseUrl || undefined,
      defaultModel: config.defaultModel || DEFAULT_PROVIDER_MODELS[providerName],
      maxTokens: config.maxTokens || 4000,
      temperature: config.temperature ?? 0.7,
      systemPrompt: config.systemPrompt || undefined,
      enabled: config.enabled ?? true
    };

    if (!providerConfig.apiKey) {
      throw new ConfigError(`API key is required for ${providerName}`);
    }

    this.config.providers[providerName] = providerConfig;
    await this.saveConfig();
  }

  public getProviderConfig(providerName?: string): ProviderConfiguration {
    const provider = providerName || this.config.defaultProvider;
    const config = this.config.providers[provider];
    
    if (!config) {
      throw new ConfigError(`Provider ${provider} is not configured`);
    }

    return { ...config };
  }

  public getProviderConfigForClient(providerName?: string): ProviderConfig {
    const config = this.getProviderConfig(providerName);
    return {
      apiKey: config.apiKey,
      baseUrl: config.baseUrl || undefined,
      defaultModel: config.defaultModel,
      maxTokens: config.maxTokens || undefined,
      temperature: config.temperature || undefined,
      systemPrompt: config.systemPrompt || undefined
    };
  }

  public isProviderConfigured(providerName: string): boolean {
    const config = this.config.providers[providerName];
    return !!(config && config.apiKey && config.enabled);
  }

  public getConfiguredProviders(): string[] {
    return Object.keys(this.config.providers).filter(provider => 
      this.isProviderConfigured(provider)
    );
  }

  public async setDefaultProvider(providerName: string): Promise<void> {
    if (!this.isProviderConfigured(providerName)) {
      throw new ConfigError(`Cannot set unconfigured provider as default: ${providerName}`);
    }

    this.config.defaultProvider = providerName;
    await this.saveConfig();
  }

  public getDefaultProvider(): string {
    return this.config.defaultProvider;
  }

  // Tool configuration
  public isToolCategoryEnabled(category: string): boolean {
    switch (category) {
      case 'filesystem':
        return this.config.tools.enableFileTools;
      case 'execution':
        return this.config.tools.enableShellTools;
      case 'web':
        return this.config.tools.enableWebTools;
      case 'memory':
        return this.config.tools.enableMemoryTools;
      default:
        return true;
    }
  }

  public requiresToolConfirmation(): boolean {
    return this.config.tools.requireConfirmation;
  }

  public isSandboxMode(): boolean {
    return this.config.tools.sandboxMode;
  }

  public getMaxFileSize(): number {
    const sizeStr = this.config.tools.maxFileSize;
    const match = sizeStr.match(/^(\d+)(KB|MB|GB)?$/i);
    
    if (!match) {
      return 10 * 1024 * 1024; // Default 10MB
    }

    const size = parseInt(match[1]);
    const unit = (match[2] || 'B').toUpperCase();

    switch (unit) {
      case 'KB':
        return size * 1024;
      case 'MB':
        return size * 1024 * 1024;
      case 'GB':
        return size * 1024 * 1024 * 1024;
      default:
        return size;
    }
  }

  public isCommandAllowed(command: string): boolean {
    const { allowedCommands, blockedCommands } = this.config.tools;
    
    // Check if explicitly blocked
    if (blockedCommands.some(blocked => command.startsWith(blocked))) {
      return false;
    }

    // If allowedCommands is empty, allow all (except blocked)
    if (allowedCommands.length === 0) {
      return true;
    }

    // Check if explicitly allowed
    return allowedCommands.some(allowed => command.startsWith(allowed));
  }

  // Security configuration
  public isPathAllowed(filePath: string): boolean {
    const { allowedPaths, blockedPaths } = this.config.security;
    const normalizedPath = path.resolve(filePath);

    // Check if explicitly blocked
    if (blockedPaths.some(blocked => normalizedPath.startsWith(blocked))) {
      return false;
    }

    // If allowedPaths is empty, allow all (except blocked)
    if (allowedPaths.length === 0) {
      return true;
    }

    // Check if explicitly allowed
    return allowedPaths.some(allowed => normalizedPath.startsWith(allowed));
  }

  public isDomainAllowed(domain: string): boolean {
    const { allowedDomains, blockedDomains } = this.config.security;

    // Check if explicitly blocked
    if (blockedDomains.includes(domain)) {
      return false;
    }

    // If allowedDomains is empty, allow all (except blocked)
    if (allowedDomains.length === 0) {
      return true;
    }

    // Check if explicitly allowed
    return allowedDomains.includes(domain);
  }

  // UI configuration
  public getUIConfig() {
    return { ...this.config.ui };
  }

  public async updateUIConfig(updates: Partial<typeof this.config.ui>): Promise<void> {
    this.config.ui = { ...this.config.ui, ...updates };
    await this.saveConfig();
  }

  // Utility methods
  public getConfigPath(): string {
    return this.configPath;
  }

  public getConfigDir(): string {
    return this.configDir;
  }

  public async resetConfig(): Promise<void> {
    this.config = { ...DEFAULT_APP_CONFIG };
    await this.saveConfig();
  }

  public async setProvider(providerName: string, config: ProviderConfiguration): Promise<void> {
    this.config.providers[providerName] = config;
    await this.saveConfig();
  }

  public async setProviderConfig(providerName: string, config: Partial<ProviderConfiguration>): Promise<void> {
    if (!this.config.providers[providerName]) {
      this.config.providers[providerName] = {
        apiKey: '',
        defaultModel: '',
        ...config
      } as ProviderConfiguration;
    } else {
      this.config.providers[providerName] = {
        ...this.config.providers[providerName],
        ...config
      };
    }
    await this.saveConfig();
  }

  public async removeProviderConfig(providerName: string): Promise<void> {
    delete this.config.providers[providerName];

    // If this was the default provider, reset to first available
    if (this.config.defaultProvider === providerName) {
      const remainingProviders = Object.keys(this.config.providers);
      this.config.defaultProvider = remainingProviders.length > 0 ? remainingProviders[0] : 'openai';
    }

    await this.saveConfig();
  }

  public async resetToDefaults(): Promise<void> {
    this.config = { ...DEFAULT_APP_CONFIG };
    await this.saveConfig();
  }

  public async exportConfig(): Promise<string> {
    return JSON.stringify(this.config, null, 2);
  }

  public async importConfig(configJson: string): Promise<void> {
    try {
      const importedConfig = JSON.parse(configJson);
      this.config = this.mergeConfig(DEFAULT_APP_CONFIG, importedConfig);
      await this.saveConfig();
    } catch (error) {
      throw new ConfigError('Failed to import config: Invalid JSON', { error });
    }
  }

  public async addToHistory(session: any): Promise<void> {
    // TODO: Implement session history storage
    console.log('Session saved to history:', session.id);
  }
}
