import { ChatMessage, SystemEvent, ConversationEvent } from '../types';
import { ConfigManager } from '../config/ConfigManager';
import * as fs from 'fs-extra';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

export interface Conversation {
  id: string;
  title: string;
  provider: string;
  model: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface ConversationSummary {
  id: string;
  title: string;
  provider: string;
  model: string;
  messageCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export class ConversationManager {
  private configManager: ConfigManager;
  private conversationsDir: string;
  private currentConversation: Conversation | null = null;
  private eventListeners: Map<string, ((event: SystemEvent) => void)[]> = new Map();

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
    this.conversationsDir = path.join(configManager.getConfigDir(), 'conversations');
    this.ensureConversationsDir();
  }

  private async ensureConversationsDir(): Promise<void> {
    await fs.ensureDir(this.conversationsDir);
  }

  public async createConversation(
    provider: string,
    model: string,
    title?: string
  ): Promise<Conversation> {
    const conversation: Conversation = {
      id: uuidv4(),
      title: title || this.generateTitle(),
      provider,
      model,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.saveConversation(conversation);
    this.currentConversation = conversation;

    this.emitEvent({
      type: 'conversation.started',
      timestamp: new Date(),
      data: {
        conversationId: conversation.id
      }
    });

    return conversation;
  }

  public async loadConversation(conversationId: string): Promise<Conversation | null> {
    try {
      const filePath = path.join(this.conversationsDir, `${conversationId}.json`);
      
      if (!(await fs.pathExists(filePath))) {
        return null;
      }

      const conversation = await fs.readJson(filePath);
      
      // Convert date strings back to Date objects
      conversation.createdAt = new Date(conversation.createdAt);
      conversation.updatedAt = new Date(conversation.updatedAt);
      conversation.messages = conversation.messages.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }));

      this.currentConversation = conversation;
      return conversation;
    } catch (error) {
      console.error(`Failed to load conversation ${conversationId}:`, error);
      return null;
    }
  }

  public async saveConversation(conversation: Conversation): Promise<void> {
    try {
      const filePath = path.join(this.conversationsDir, `${conversation.id}.json`);
      conversation.updatedAt = new Date();
      await fs.writeJson(filePath, conversation, { spaces: 2 });
    } catch (error) {
      console.error(`Failed to save conversation ${conversation.id}:`, error);
      throw error;
    }
  }

  public async deleteConversation(conversationId: string): Promise<boolean> {
    try {
      const filePath = path.join(this.conversationsDir, `${conversationId}.json`);
      
      if (await fs.pathExists(filePath)) {
        await fs.remove(filePath);
        
        if (this.currentConversation?.id === conversationId) {
          this.currentConversation = null;
        }

        this.emitEvent({
          type: 'conversation.ended',
          timestamp: new Date(),
          data: {
            conversationId
          }
        });

        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`Failed to delete conversation ${conversationId}:`, error);
      return false;
    }
  }

  public async addMessage(message: ChatMessage): Promise<void> {
    if (!this.currentConversation) {
      throw new Error('No active conversation');
    }

    this.currentConversation.messages.push(message);
    await this.saveConversation(this.currentConversation);

    this.emitEvent({
      type: message.role === 'user' ? 'message.sent' : 'message.received',
      timestamp: new Date(),
      data: {
        conversationId: this.currentConversation.id,
        message
      }
    });
  }

  public async updateMessage(messageId: string, updates: Partial<ChatMessage>): Promise<boolean> {
    if (!this.currentConversation) {
      return false;
    }

    const messageIndex = this.currentConversation.messages.findIndex(msg => msg.id === messageId);
    if (messageIndex === -1) {
      return false;
    }

    this.currentConversation.messages[messageIndex] = {
      ...this.currentConversation.messages[messageIndex],
      ...updates
    };

    await this.saveConversation(this.currentConversation);
    return true;
  }

  public async getConversationHistory(limit?: number): Promise<ConversationSummary[]> {
    try {
      const files = await fs.readdir(this.conversationsDir);
      const jsonFiles = files.filter(file => file.endsWith('.json'));

      const summaries: ConversationSummary[] = [];

      for (const file of jsonFiles) {
        try {
          const filePath = path.join(this.conversationsDir, file);
          const conversation = await fs.readJson(filePath);
          
          summaries.push({
            id: conversation.id,
            title: conversation.title,
            provider: conversation.provider,
            model: conversation.model,
            messageCount: conversation.messages.length,
            createdAt: new Date(conversation.createdAt),
            updatedAt: new Date(conversation.updatedAt)
          });
        } catch (error) {
          console.error(`Failed to read conversation file ${file}:`, error);
        }
      }

      // Sort by updated date (most recent first)
      summaries.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());

      return limit ? summaries.slice(0, limit) : summaries;
    } catch (error) {
      console.error('Failed to get conversation history:', error);
      return [];
    }
  }

  public getCurrentConversation(): Conversation | null {
    return this.currentConversation;
  }

  public async searchConversations(query: string): Promise<ConversationSummary[]> {
    const allConversations = await this.getConversationHistory();
    const lowerQuery = query.toLowerCase();

    return allConversations.filter(conv => 
      conv.title.toLowerCase().includes(lowerQuery) ||
      conv.provider.toLowerCase().includes(lowerQuery) ||
      conv.model.toLowerCase().includes(lowerQuery)
    );
  }

  public async exportConversation(conversationId: string): Promise<string | null> {
    const conversation = await this.loadConversation(conversationId);
    if (!conversation) {
      return null;
    }

    return JSON.stringify(conversation, null, 2);
  }

  public async importConversation(conversationData: string): Promise<Conversation | null> {
    try {
      const conversation = JSON.parse(conversationData);
      
      // Validate conversation structure
      if (!conversation.id || !conversation.messages || !Array.isArray(conversation.messages)) {
        throw new Error('Invalid conversation format');
      }

      // Generate new ID to avoid conflicts
      conversation.id = uuidv4();
      conversation.createdAt = new Date(conversation.createdAt);
      conversation.updatedAt = new Date();
      conversation.messages = conversation.messages.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }));

      await this.saveConversation(conversation);
      return conversation;
    } catch (error) {
      console.error('Failed to import conversation:', error);
      return null;
    }
  }

  public async cleanupOldConversations(maxAge: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - maxAge);

    const conversations = await this.getConversationHistory();
    let deletedCount = 0;

    for (const conv of conversations) {
      if (conv.updatedAt < cutoffDate) {
        const deleted = await this.deleteConversation(conv.id);
        if (deleted) {
          deletedCount++;
        }
      }
    }

    return deletedCount;
  }

  private generateTitle(): string {
    const now = new Date();
    return `Conversation ${now.toLocaleDateString()} ${now.toLocaleTimeString()}`;
  }

  // Event system
  public addEventListener(eventType: string, listener: (event: SystemEvent) => void): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    this.eventListeners.get(eventType)!.push(listener);
  }

  public removeEventListener(eventType: string, listener: (event: SystemEvent) => void): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emitEvent(event: ConversationEvent): void {
    const listeners = this.eventListeners.get(event.type);
    if (listeners) {
      listeners.forEach(listener => listener(event));
    }

    // Also emit to 'all' listeners
    const allListeners = this.eventListeners.get('all');
    if (allListeners) {
      allListeners.forEach(listener => listener(event));
    }
  }
}
