import { GoogleGenerativeA<PERSON>, GenerativeModel, ChatSession } from '@google/generative-ai';
import { BaseProvider, ProviderConfig, ProviderOptions } from './BaseProvider';
import { ChatMessage, BaseTool } from '../types';

export class GoogleProvider extends BaseProvider {
  public name = 'google';
  private client: GoogleGenerativeAI;
  private model: GenerativeModel | null = null;

  constructor(config: ProviderConfig) {
    super(config);
    this.validateConfig();
    
    this.client = new GoogleGenerativeAI(this.config.apiKey);
  }

  public async sendMessage(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: ProviderOptions
  ): Promise<ChatMessage> {
    try {
      const requestOptions = this.getRequestOptions(options);
      const model = this.getModel(requestOptions.model, tools);
      
      const formattedMessages = this.formatMessages(messages);
      const lastMessage = formattedMessages[formattedMessages.length - 1];

      let result;
      
      if (formattedMessages.length === 1) {
        // Single message
        result = await model.generateContent(lastMessage.parts);
      } else {
        // Multi-turn conversation
        const chat = model.startChat({
          history: formattedMessages.slice(0, -1),
          generationConfig: {
            temperature: requestOptions.temperature,
            maxOutputTokens: requestOptions.maxTokens,
          },
        });
        
        result = await chat.sendMessage(lastMessage.parts);
      }

      return this.processResponse(result, requestOptions.model);
    } catch (error) {
      throw this.handleError(error, 'sendMessage');
    }
  }

  public async *streamMessage(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: ProviderOptions
  ): AsyncGenerator<string, ChatMessage> {
    try {
      const requestOptions = this.getRequestOptions(options);
      const model = this.getModel(requestOptions.model, tools);
      
      const formattedMessages = this.formatMessages(messages);
      const lastMessage = formattedMessages[formattedMessages.length - 1];

      let result;
      
      if (formattedMessages.length === 1) {
        // Single message
        result = await model.generateContentStream(lastMessage.parts);
      } else {
        // Multi-turn conversation
        const chat = model.startChat({
          history: formattedMessages.slice(0, -1),
          generationConfig: {
            temperature: requestOptions.temperature,
            maxOutputTokens: requestOptions.maxTokens,
          },
        });
        
        result = await chat.sendMessageStream(lastMessage.parts);
      }

      let fullContent = '';
      let toolCalls: any[] = [];

      for await (const chunk of result.stream) {
        const text = chunk.text();
        if (text) {
          fullContent += text;
          yield text;
        }

        // Handle function calls
        const functionCalls = chunk.functionCalls();
        if (functionCalls) {
          toolCalls.push(...functionCalls);
        }
      }

      const finalResult = await result.response;
      
      return this.createChatMessage(fullContent, 'assistant', {
        provider: this.name,
        model: requestOptions.model,
        usage: finalResult.usageMetadata ? {
          promptTokens: finalResult.usageMetadata.promptTokenCount,
          completionTokens: finalResult.usageMetadata.candidatesTokenCount,
          totalTokens: finalResult.usageMetadata.totalTokenCount,
        } : undefined,
        toolCalls: toolCalls.length > 0 ? this.formatToolCalls(toolCalls) : undefined,
      });
    } catch (error) {
      throw this.handleError(error, 'streamMessage');
    }
  }

  private getModel(modelName: string, tools?: BaseTool[]): GenerativeModel {
    const modelConfig: any = {
      model: modelName,
    };

    if (this.config.systemPrompt) {
      modelConfig.systemInstruction = this.config.systemPrompt;
    }

    if (tools && tools.length > 0) {
      modelConfig.tools = [{
        functionDeclarations: this.formatTools(tools),
      }];
    }

    return this.client.getGenerativeModel(modelConfig);
  }

  private processResponse(result: any, model: string): ChatMessage {
    const response = result.response;
    const text = response.text() || '';
    
    let toolCalls: any[] = [];
    const functionCalls = response.functionCalls();
    if (functionCalls) {
      toolCalls = this.formatToolCalls(functionCalls);
    }

    return this.createChatMessage(text, 'assistant', {
      provider: this.name,
      model,
      usage: response.usageMetadata ? {
        promptTokens: response.usageMetadata.promptTokenCount,
        completionTokens: response.usageMetadata.candidatesTokenCount,
        totalTokens: response.usageMetadata.totalTokenCount,
      } : undefined,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      finishReason: response.candidates?.[0]?.finishReason,
    });
  }

  private formatToolCalls(functionCalls: any[]): any[] {
    return functionCalls.map(call => ({
      id: this.generateId(),
      name: call.name,
      parameters: call.args,
    }));
  }

  protected formatMessages(messages: ChatMessage[]): any[] {
    const formatted: any[] = [];

    for (const msg of messages) {
      if (msg.role === 'system') {
        // System messages are handled in model configuration
        continue;
      }

      const role = msg.role === 'assistant' ? 'model' : 'user';
      const parts: any[] = [{ text: msg.content }];

      // Handle tool calls in assistant messages
      if (msg.role === 'assistant' && msg.toolCalls) {
        for (const call of msg.toolCalls) {
          parts.push({
            functionCall: {
              name: call.name,
              args: call.parameters,
            },
          });
        }
      }

      // Handle tool responses
      if (msg.role === 'tool') {
        parts[0] = {
          functionResponse: {
            name: msg.metadata?.toolName,
            response: {
              content: msg.content,
            },
          },
        };
      }

      formatted.push({ role, parts });
    }

    return formatted;
  }

  protected formatTools(tools?: BaseTool[]): any[] {
    if (!tools || tools.length === 0) {
      return [];
    }

    return tools.map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: {
        type: 'object',
        properties: tool.parameters.properties || {},
        required: tool.parameters.required || [],
      },
    }));
  }

  public async testConnection(): Promise<boolean> {
    try {
      const model = this.client.getGenerativeModel({ model: this.config.defaultModel });
      const result = await model.generateContent('Hello');
      return !!result.response.text();
    } catch (error) {
      console.error('Google connection test failed:', error);
      return false;
    }
  }

  public async getAvailableModels(): Promise<string[]> {
    try {
      const models = await this.client.listModels();
      return models
        .filter(model => model.name.includes('gemini'))
        .map(model => model.name.split('/').pop() || model.name)
        .sort();
    } catch (error) {
      console.error('Failed to fetch Google models:', error);
      return ['gemini-pro', 'gemini-pro-vision']; // Fallback to known models
    }
  }

  protected validateConfig(): void {
    super.validateConfig();
    
    // Google API keys are typically 39 characters long
    if (this.config.apiKey.length < 30) {
      throw new Error('Google API key appears to be invalid (too short)');
    }
  }
}
