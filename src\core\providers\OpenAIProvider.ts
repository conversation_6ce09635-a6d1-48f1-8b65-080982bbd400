import OpenAI from 'openai';
import { BaseProvider, ProviderConfig, ProviderOptions } from './BaseProvider';
import { ChatMessage, BaseTool } from '../types';

export class OpenAIProvider extends BaseProvider {
  public name = 'openai';
  private client: OpenAI;

  constructor(config: ProviderConfig) {
    super(config);
    this.validateConfig();
    
    this.client = new OpenAI({
      apiKey: this.config.apiKey,
      baseURL: this.config.baseUrl,
    });
  }

  public async sendMessage(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: ProviderOptions
  ): Promise<ChatMessage> {
    try {
      const requestOptions = this.getRequestOptions(options);
      const formattedMessages = this.formatMessages(messages);
      const formattedTools = this.formatTools(tools);

      const requestParams: OpenAI.Chat.ChatCompletionCreateParams = {
        model: requestOptions.model,
        messages: formattedMessages as OpenAI.Chat.ChatCompletionMessageParam[],
        max_tokens: requestOptions.maxTokens,
        temperature: requestOptions.temperature,
        ...(formattedTools.length > 0 && { tools: formattedTools }),
      };

      if (this.config.systemPrompt) {
        requestParams.messages.unshift({
          role: 'system',
          content: this.config.systemPrompt,
        });
      }

      const response = await this.client.chat.completions.create(requestParams);
      
      return this.processResponse(response);
    } catch (error) {
      throw this.handleError(error, 'sendMessage');
    }
  }

  public async *streamMessage(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: ProviderOptions
  ): AsyncGenerator<string, ChatMessage> {
    try {
      const requestOptions = this.getRequestOptions(options);
      const formattedMessages = this.formatMessages(messages);
      const formattedTools = this.formatTools(tools);

      const requestParams: OpenAI.Chat.ChatCompletionCreateParams = {
        model: requestOptions.model,
        messages: formattedMessages as OpenAI.Chat.ChatCompletionMessageParam[],
        max_tokens: requestOptions.maxTokens,
        temperature: requestOptions.temperature,
        stream: true,
        ...(formattedTools.length > 0 && { tools: formattedTools }),
      };

      if (this.config.systemPrompt) {
        requestParams.messages.unshift({
          role: 'system',
          content: this.config.systemPrompt,
        });
      }

      const stream = await this.client.chat.completions.create(requestParams);
      
      let fullContent = '';
      let toolCalls: any[] = [];
      let usage: any = null;

      for await (const chunk of stream) {
        const delta = chunk.choices[0]?.delta;
        
        if (delta?.content) {
          fullContent += delta.content;
          yield delta.content;
        }

        if (delta?.tool_calls) {
          toolCalls.push(...delta.tool_calls);
        }

        if (chunk.usage) {
          usage = chunk.usage;
        }
      }

      return this.createChatMessage(fullContent, 'assistant', {
        provider: this.name,
        model: requestOptions.model,
        usage: usage ? {
          promptTokens: usage.prompt_tokens,
          completionTokens: usage.completion_tokens,
          totalTokens: usage.total_tokens,
        } : undefined,
        toolCalls: toolCalls.length > 0 ? this.formatToolCalls(toolCalls) : undefined,
      });
    } catch (error) {
      throw this.handleError(error, 'streamMessage');
    }
  }

  private processResponse(response: OpenAI.Chat.ChatCompletionResponse): ChatMessage {
    const choice = response.choices[0];
    const message = choice.message;
    
    const content = message.content || '';
    const toolCalls = message.tool_calls ? this.formatToolCalls(message.tool_calls) : undefined;

    return this.createChatMessage(content, 'assistant', {
      provider: this.name,
      model: response.model,
      usage: response.usage ? {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens,
      } : undefined,
      toolCalls,
      finishReason: choice.finish_reason,
    });
  }

  private formatToolCalls(toolCalls: any[]): any[] {
    return toolCalls.map(call => ({
      id: call.id,
      name: call.function.name,
      parameters: JSON.parse(call.function.arguments || '{}'),
    }));
  }

  protected formatMessages(messages: ChatMessage[]): any[] {
    return messages.map(msg => {
      const formatted: any = {
        role: msg.role,
        content: msg.content,
      };

      // Handle tool calls in assistant messages
      if (msg.role === 'assistant' && msg.toolCalls) {
        formatted.tool_calls = msg.toolCalls.map((call: any) => ({
          id: call.id,
          type: 'function',
          function: {
            name: call.name,
            arguments: JSON.stringify(call.parameters),
          },
        }));
      }

      // Handle tool responses
      if (msg.role === 'tool') {
        formatted.tool_call_id = msg.metadata?.toolCallId;
      }

      return formatted;
    });
  }

  public async testConnection(): Promise<boolean> {
    try {
      const response = await this.client.models.list();
      return response.data.length > 0;
    } catch (error) {
      console.error('OpenAI connection test failed:', error);
      return false;
    }
  }

  public async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.models.list();
      return response.data
        .filter(model => model.id.includes('gpt'))
        .map(model => model.id)
        .sort();
    } catch (error) {
      console.error('Failed to fetch OpenAI models:', error);
      return ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo']; // Fallback to known models
    }
  }

  protected validateConfig(): void {
    super.validateConfig();
    
    if (!this.config.apiKey.startsWith('sk-')) {
      throw new Error('OpenAI API key should start with "sk-"');
    }
  }
}
