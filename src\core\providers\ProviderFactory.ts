import { BaseProvider, ProviderConfig } from './BaseProvider';
import { OpenAIProvider } from './OpenAIProvider';
import { AnthropicProvider } from './AnthropicProvider';
import { GoogleProvider } from './GoogleProvider';
import { DeepseekProvider } from './DeepseekProvider';
import { SUPPORTED_PROVIDERS } from '../types';

export class ProviderFactory {
  private static providers: Map<string, BaseProvider> = new Map();

  public static createProvider(providerName: string, config: ProviderConfig): BaseProvider {
    if (!SUPPORTED_PROVIDERS[providerName]) {
      throw new Error(`Unsupported provider: ${providerName}`);
    }

    // Check if provider instance already exists and config hasn't changed
    const existingProvider = this.providers.get(providerName);
    if (existingProvider && this.configMatches(existingProvider.getConfig(), config)) {
      return existingProvider;
    }

    let provider: BaseProvider;

    switch (providerName) {
      case 'openai':
        provider = new OpenAIProvider(config);
        break;
      case 'anthropic':
        provider = new AnthropicProvider(config);
        break;
      case 'google':
        provider = new GoogleProvider(config);
        break;
      case 'deepseek':
        provider = new DeepseekProvider(config);
        break;
      default:
        throw new Error(`Provider implementation not found: ${providerName}`);
    }

    this.providers.set(providerName, provider);
    return provider;
  }

  public static getProvider(providerName: string): BaseProvider | null {
    return this.providers.get(providerName) || null;
  }

  public static hasProvider(providerName: string): boolean {
    return this.providers.has(providerName);
  }

  public static removeProvider(providerName: string): void {
    this.providers.delete(providerName);
  }

  public static clearProviders(): void {
    this.providers.clear();
  }

  public static getLoadedProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  public static async testProvider(providerName: string, config: ProviderConfig): Promise<boolean> {
    try {
      const provider = this.createProvider(providerName, config);
      return await provider.testConnection();
    } catch (error) {
      console.error(`Failed to test provider ${providerName}:`, error);
      return false;
    }
  }

  public static validateProviderConfig(providerName: string, config: Partial<ProviderConfig>): string[] {
    const errors: string[] = [];
    const providerInfo = SUPPORTED_PROVIDERS[providerName];

    if (!providerInfo) {
      errors.push(`Unknown provider: ${providerName}`);
      return errors;
    }

    if (!config.apiKey) {
      errors.push('API key is required');
    } else {
      // Provider-specific API key validation
      switch (providerName) {
        case 'openai':
          if (!config.apiKey.startsWith('sk-')) {
            errors.push('OpenAI API key should start with "sk-"');
          }
          break;
        case 'anthropic':
          if (!config.apiKey.startsWith('sk-ant-')) {
            errors.push('Anthropic API key should start with "sk-ant-"');
          }
          break;
        case 'google':
          if (config.apiKey.length < 30) {
            errors.push('Google API key appears to be invalid (too short)');
          }
          break;
        case 'deepseek':
          // Deepseek doesn't have a specific format requirement
          break;
      }
    }

    if (!config.defaultModel) {
      errors.push('Default model is required');
    } else if (!providerInfo.models.includes(config.defaultModel)) {
      errors.push(`Model "${config.defaultModel}" is not supported by ${providerInfo.displayName}`);
    }

    if (config.baseUrl && !this.isValidUrl(config.baseUrl)) {
      errors.push('Base URL must be a valid URL');
    }

    if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 100000)) {
      errors.push('Max tokens must be between 1 and 100000');
    }

    if (config.temperature !== undefined && (config.temperature < 0 || config.temperature > 2)) {
      errors.push('Temperature must be between 0 and 2');
    }

    return errors;
  }

  public static getProviderCapabilities(providerName: string): {
    supportsStreaming: boolean;
    supportsTools: boolean;
    supportsVision: boolean;
    maxTokens: number;
    supportedModels: string[];
  } {
    const providerInfo = SUPPORTED_PROVIDERS[providerName];
    if (!providerInfo) {
      throw new Error(`Unknown provider: ${providerName}`);
    }

    const capabilities = {
      supportsStreaming: true, // All providers support streaming
      supportsTools: true, // All providers support tools
      supportsVision: false,
      maxTokens: 4000,
      supportedModels: providerInfo.models,
    };

    switch (providerName) {
      case 'openai':
        capabilities.supportsVision = true;
        capabilities.maxTokens = 128000; // GPT-4 Turbo
        break;
      case 'anthropic':
        capabilities.supportsVision = true;
        capabilities.maxTokens = 200000; // Claude-3
        break;
      case 'google':
        capabilities.supportsVision = providerInfo.models.includes('gemini-pro-vision');
        capabilities.maxTokens = 32000; // Gemini Pro
        break;
      case 'deepseek':
        capabilities.maxTokens = 32000; // Deepseek models
        break;
    }

    return capabilities;
  }

  private static configMatches(config1: ProviderConfig, config2: ProviderConfig): boolean {
    return (
      config1.apiKey === config2.apiKey &&
      config1.baseUrl === config2.baseUrl &&
      config1.defaultModel === config2.defaultModel &&
      config1.maxTokens === config2.maxTokens &&
      config1.temperature === config2.temperature &&
      config1.systemPrompt === config2.systemPrompt
    );
  }

  private static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  public static getSupportedProviders(): string[] {
    return Object.keys(SUPPORTED_PROVIDERS);
  }

  public static getProviderInfo(providerName: string) {
    return SUPPORTED_PROVIDERS[providerName];
  }

  public static async getAvailableModels(providerName: string, config: ProviderConfig): Promise<string[]> {
    try {
      const provider = this.createProvider(providerName, config);
      
      // Check if provider has getAvailableModels method
      if ('getAvailableModels' in provider && typeof provider.getAvailableModels === 'function') {
        return await (provider as any).getAvailableModels();
      }
      
      // Fallback to static model list
      return SUPPORTED_PROVIDERS[providerName].models;
    } catch (error) {
      console.error(`Failed to get models for ${providerName}:`, error);
      return SUPPORTED_PROVIDERS[providerName].models;
    }
  }
}
