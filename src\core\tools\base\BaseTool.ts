import { BaseTool as IBaseTool, ToolResult } from '../../types';

export interface ToolMetadata {
  category: 'filesystem' | 'execution' | 'web' | 'memory' | 'mcp' | 'utility';
  tags: string[];
  version: string;
  author?: string;
  dangerous: boolean;
  requiresConfirmation: boolean;
}

export interface ToolValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export abstract class BaseTool implements IBaseTool {
  public abstract name: string;
  public abstract description: string;
  public abstract parameters: Record<string, any>;
  public abstract requiresConfirmation: boolean;
  
  protected metadata: ToolMetadata;

  constructor(metadata: ToolMetadata) {
    this.metadata = metadata;
  }

  public abstract validate(params: Record<string, any>): Promise<ToolValidationResult>;
  public abstract execute(params: Record<string, any>): Promise<ToolResult>;

  public async shouldConfirmExecute(params: Record<string, any>): Promise<boolean> {
    // Default implementation - can be overridden
    return this.requiresConfirmation || this.metadata.dangerous;
  }

  public getMetadata(): ToolMetadata {
    return { ...this.metadata };
  }

  public getCategory(): string {
    return this.metadata.category;
  }

  public getTags(): string[] {
    return [...this.metadata.tags];
  }

  public isDangerous(): boolean {
    return this.metadata.dangerous;
  }

  public getVersion(): string {
    return this.metadata.version;
  }

  protected createSuccessResult(
    content: string,
    displayContent?: string,
    metadata?: Record<string, any>
  ): ToolResult {
    return {
      success: true,
      content,
      displayContent,
      metadata,
    };
  }

  protected createErrorResult(
    error: string,
    metadata?: Record<string, any>
  ): ToolResult {
    return {
      success: false,
      content: '',
      error,
      metadata,
    };
  }

  protected validateRequiredParams(
    params: Record<string, any>,
    requiredParams: string[]
  ): ToolValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const param of requiredParams) {
      if (!(param in params) || params[param] === undefined || params[param] === null) {
        errors.push(`Required parameter '${param}' is missing`);
      } else if (typeof params[param] === 'string' && params[param].trim() === '') {
        errors.push(`Required parameter '${param}' cannot be empty`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  protected validateStringParam(
    value: any,
    paramName: string,
    options?: {
      minLength?: number;
      maxLength?: number;
      pattern?: RegExp;
      allowEmpty?: boolean;
    }
  ): string[] {
    const errors: string[] = [];

    if (typeof value !== 'string') {
      errors.push(`Parameter '${paramName}' must be a string`);
      return errors;
    }

    if (!options?.allowEmpty && value.trim() === '') {
      errors.push(`Parameter '${paramName}' cannot be empty`);
      return errors;
    }

    if (options?.minLength && value.length < options.minLength) {
      errors.push(`Parameter '${paramName}' must be at least ${options.minLength} characters`);
    }

    if (options?.maxLength && value.length > options.maxLength) {
      errors.push(`Parameter '${paramName}' must be at most ${options.maxLength} characters`);
    }

    if (options?.pattern && !options.pattern.test(value)) {
      errors.push(`Parameter '${paramName}' does not match required pattern`);
    }

    return errors;
  }

  protected validateNumberParam(
    value: any,
    paramName: string,
    options?: {
      min?: number;
      max?: number;
      integer?: boolean;
    }
  ): string[] {
    const errors: string[] = [];

    if (typeof value !== 'number' || isNaN(value)) {
      errors.push(`Parameter '${paramName}' must be a valid number`);
      return errors;
    }

    if (options?.integer && !Number.isInteger(value)) {
      errors.push(`Parameter '${paramName}' must be an integer`);
    }

    if (options?.min !== undefined && value < options.min) {
      errors.push(`Parameter '${paramName}' must be at least ${options.min}`);
    }

    if (options?.max !== undefined && value > options.max) {
      errors.push(`Parameter '${paramName}' must be at most ${options.max}`);
    }

    return errors;
  }

  protected validateBooleanParam(value: any, paramName: string): string[] {
    if (typeof value !== 'boolean') {
      return [`Parameter '${paramName}' must be a boolean`];
    }
    return [];
  }

  protected validateArrayParam(
    value: any,
    paramName: string,
    options?: {
      minLength?: number;
      maxLength?: number;
      itemType?: 'string' | 'number' | 'boolean' | 'object';
    }
  ): string[] {
    const errors: string[] = [];

    if (!Array.isArray(value)) {
      errors.push(`Parameter '${paramName}' must be an array`);
      return errors;
    }

    if (options?.minLength && value.length < options.minLength) {
      errors.push(`Parameter '${paramName}' must have at least ${options.minLength} items`);
    }

    if (options?.maxLength && value.length > options.maxLength) {
      errors.push(`Parameter '${paramName}' must have at most ${options.maxLength} items`);
    }

    if (options?.itemType) {
      for (let i = 0; i < value.length; i++) {
        const item = value[i];
        const itemType = typeof item;
        
        if (options.itemType === 'object' && (itemType !== 'object' || item === null)) {
          errors.push(`Parameter '${paramName}[${i}]' must be an object`);
        } else if (options.itemType !== 'object' && itemType !== options.itemType) {
          errors.push(`Parameter '${paramName}[${i}]' must be a ${options.itemType}`);
        }
      }
    }

    return errors;
  }

  protected validatePathParam(value: any, paramName: string): string[] {
    const errors = this.validateStringParam(value, paramName);
    if (errors.length > 0) return errors;

    const path = value as string;
    
    // Check for path traversal attempts
    if (path.includes('..') || path.includes('~')) {
      errors.push(`Parameter '${paramName}' contains invalid path characters`);
    }

    // Check for absolute paths (might be restricted in some contexts)
    if (path.startsWith('/') || /^[A-Za-z]:/.test(path)) {
      // This is an absolute path - might be valid depending on context
      // Individual tools can add additional validation
    }

    return errors;
  }

  protected sanitizePath(path: string): string {
    // Remove dangerous path components
    return path
      .replace(/\.\./g, '') // Remove parent directory references
      .replace(/~/g, '') // Remove home directory references
      .replace(/\/+/g, '/') // Normalize multiple slashes
      .trim();
  }

  protected formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  protected truncateContent(content: string, maxLength: number = 1000): string {
    if (content.length <= maxLength) {
      return content;
    }
    
    return content.substring(0, maxLength - 3) + '...';
  }

  public toString(): string {
    return `${this.name} (${this.metadata.category})`;
  }
}
