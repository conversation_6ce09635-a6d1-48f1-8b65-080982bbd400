import { ToolRegistry, ToolExecutionContext, ToolExecutionResult } from './ToolRegistry';
import { BaseTool } from './BaseTool';

export interface ToolConfirmationHandler {
  confirmExecution(toolName: string, parameters: Record<string, any>): Promise<boolean>;
}

export interface ToolExecutionOptions {
  requireConfirmation?: boolean;
  timeout?: number;
  retries?: number;
  context?: ToolExecutionContext;
}

export interface ToolExecutionPlan {
  toolName: string;
  parameters: Record<string, any>;
  dependencies?: string[];
  parallel?: boolean;
}

export class ToolExecutionEngine {
  private registry: ToolRegistry;
  private confirmationHandler?: ToolConfirmationHandler;
  private defaultTimeout: number = 30000; // 30 seconds
  private defaultRetries: number = 0;

  constructor(registry: ToolRegistry, confirmationHandler?: ToolConfirmationHandler) {
    this.registry = registry;
    this.confirmationHandler = confirmationHandler;
  }

  public setConfirmationHandler(handler: Too<PERSON><PERSON>onfirmationHand<PERSON>): void {
    this.confirmationHandler = handler;
  }

  public async executeSingleTool(
    toolName: string,
    parameters: Record<string, any>,
    options: ToolExecutionOptions = {}
  ): Promise<ToolExecutionResult> {
    const {
      requireConfirmation = true,
      timeout = this.defaultTimeout,
      retries = this.defaultRetries,
      context = {},
    } = options;

    // Check if tool requires confirmation
    if (requireConfirmation && await this.registry.shouldConfirmExecution(toolName, parameters)) {
      if (!this.confirmationHandler) {
        return {
          success: false,
          content: '',
          error: 'Tool execution requires confirmation but no confirmation handler is available',
          toolName,
          executionTime: 0,
          context,
        };
      }

      const confirmed = await this.confirmationHandler.confirmExecution(toolName, parameters);
      if (!confirmed) {
        return {
          success: false,
          content: '',
          error: 'Tool execution was denied by user',
          toolName,
          executionTime: 0,
          context,
        };
      }
    }

    // Execute with retries
    let lastError: string = '';
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const result = await this.executeWithTimeout(toolName, parameters, context, timeout);
        
        if (result.success || attempt === retries) {
          return result;
        }
        
        lastError = result.error || 'Unknown error';
        
        // Wait before retry (exponential backoff)
        if (attempt < retries) {
          await this.delay(Math.pow(2, attempt) * 1000);
        }
      } catch (error) {
        lastError = error instanceof Error ? error.message : String(error);
        
        if (attempt === retries) {
          return {
            success: false,
            content: '',
            error: `Failed after ${retries + 1} attempts: ${lastError}`,
            toolName,
            executionTime: 0,
            context,
          };
        }
      }
    }

    return {
      success: false,
      content: '',
      error: `Failed after ${retries + 1} attempts: ${lastError}`,
      toolName,
      executionTime: 0,
      context,
    };
  }

  public async executeMultipleTools(
    plans: ToolExecutionPlan[],
    options: ToolExecutionOptions = {}
  ): Promise<ToolExecutionResult[]> {
    const results: ToolExecutionResult[] = [];
    const executed = new Set<string>();
    const pending = [...plans];

    while (pending.length > 0) {
      const readyToExecute = pending.filter(plan => 
        !plan.dependencies || plan.dependencies.every(dep => executed.has(dep))
      );

      if (readyToExecute.length === 0) {
        // Circular dependency or missing dependency
        const remainingPlans = pending.map(p => p.toolName).join(', ');
        throw new Error(`Circular dependency or missing dependency detected in tools: ${remainingPlans}`);
      }

      // Separate parallel and sequential executions
      const parallelPlans = readyToExecute.filter(plan => plan.parallel);
      const sequentialPlans = readyToExecute.filter(plan => !plan.parallel);

      // Execute parallel tools
      if (parallelPlans.length > 0) {
        const parallelPromises = parallelPlans.map(plan =>
          this.executeSingleTool(plan.toolName, plan.parameters, options)
        );
        
        const parallelResults = await Promise.all(parallelPromises);
        results.push(...parallelResults);
        
        parallelPlans.forEach(plan => {
          executed.add(plan.toolName);
          pending.splice(pending.indexOf(plan), 1);
        });
      }

      // Execute sequential tools
      for (const plan of sequentialPlans) {
        const result = await this.executeSingleTool(plan.toolName, plan.parameters, options);
        results.push(result);
        executed.add(plan.toolName);
        pending.splice(pending.indexOf(plan), 1);

        // Stop execution if a critical tool fails
        if (!result.success && this.isCriticalTool(plan.toolName)) {
          throw new Error(`Critical tool '${plan.toolName}' failed: ${result.error}`);
        }
      }
    }

    return results;
  }

  public async executeToolChain(
    toolChain: Array<{
      toolName: string;
      parameters: Record<string, any>;
      useOutputFrom?: string; // Use output from previous tool
    }>,
    options: ToolExecutionOptions = {}
  ): Promise<ToolExecutionResult[]> {
    const results: ToolExecutionResult[] = [];
    const outputs: Record<string, any> = {};

    for (const step of toolChain) {
      let parameters = { ...step.parameters };

      // Inject output from previous tool if specified
      if (step.useOutputFrom && outputs[step.useOutputFrom]) {
        parameters = { ...parameters, input: outputs[step.useOutputFrom] };
      }

      const result = await this.executeSingleTool(step.toolName, parameters, options);
      results.push(result);

      // Store output for next tool
      if (result.success) {
        outputs[step.toolName] = result.content;
      } else {
        // Chain execution failed
        break;
      }
    }

    return results;
  }

  private async executeWithTimeout(
    toolName: string,
    parameters: Record<string, any>,
    context: ToolExecutionContext,
    timeout: number
  ): Promise<ToolExecutionResult> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Tool execution timed out after ${timeout}ms`));
      }, timeout);

      this.registry.executeTool(toolName, parameters, context)
        .then(result => {
          clearTimeout(timeoutId);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeoutId);
          reject(error);
        });
    });
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private isCriticalTool(toolName: string): boolean {
    // Define which tools are critical and should stop execution on failure
    const criticalTools = ['write_file', 'run_shell_command'];
    return criticalTools.includes(toolName);
  }

  public setDefaultTimeout(timeout: number): void {
    this.defaultTimeout = Math.max(1000, timeout);
  }

  public setDefaultRetries(retries: number): void {
    this.defaultRetries = Math.max(0, retries);
  }

  public getRegistry(): ToolRegistry {
    return this.registry;
  }

  public async validateToolChain(
    toolChain: Array<{
      toolName: string;
      parameters: Record<string, any>;
    }>
  ): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    for (let i = 0; i < toolChain.length; i++) {
      const step = toolChain[i];
      const validation = await this.registry.validateToolExecution(step.toolName, step.parameters);
      
      if (!validation.valid) {
        errors.push(`Step ${i + 1} (${step.toolName}): ${validation.errors.join(', ')}`);
      }
      
      warnings.push(...validation.warnings.map(w => `Step ${i + 1} (${step.toolName}): ${w}`));
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }
}
