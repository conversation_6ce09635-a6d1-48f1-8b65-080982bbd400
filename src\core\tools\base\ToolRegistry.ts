import { BaseTool, ToolMetadata, ToolValidationResult } from './BaseTool';
import { ToolResult } from '../../types';

export interface ToolExecutionContext {
  userId?: string;
  sessionId?: string;
  workingDirectory?: string;
  environment?: Record<string, string>;
  permissions?: string[];
}

export interface ToolExecutionResult extends ToolResult {
  toolName: string;
  executionTime: number;
  context: ToolExecutionContext;
}

export interface ToolFilter {
  category?: string;
  tags?: string[];
  dangerous?: boolean;
  requiresConfirmation?: boolean;
  enabled?: boolean;
}

export class ToolRegistry {
  private tools: Map<string, BaseTool> = new Map();
  private enabledTools: Set<string> = new Set();
  private disabledTools: Set<string> = new Set();
  private toolExecutionHistory: ToolExecutionResult[] = [];
  private maxHistorySize: number = 1000;

  public registerTool(tool: BaseTool): void {
    if (this.tools.has(tool.name)) {
      throw new Error(`Tool with name '${tool.name}' is already registered`);
    }

    this.tools.set(tool.name, tool);
    this.enabledTools.add(tool.name);
  }

  public unregisterTool(toolName: string): boolean {
    const removed = this.tools.delete(toolName);
    this.enabledTools.delete(toolName);
    this.disabledTools.delete(toolName);
    return removed;
  }

  public getTool(toolName: string): BaseTool | null {
    return this.tools.get(toolName) || null;
  }

  public getAllTools(): BaseTool[] {
    return Array.from(this.tools.values());
  }

  public getEnabledTools(): BaseTool[] {
    return Array.from(this.tools.values()).filter(tool => 
      this.enabledTools.has(tool.name) && !this.disabledTools.has(tool.name)
    );
  }

  public getToolsByCategory(category: string): BaseTool[] {
    return this.getAllTools().filter(tool => tool.getCategory() === category);
  }

  public getToolsByTag(tag: string): BaseTool[] {
    return this.getAllTools().filter(tool => tool.getTags().includes(tag));
  }

  public filterTools(filter: ToolFilter): BaseTool[] {
    return this.getAllTools().filter(tool => {
      if (filter.category && tool.getCategory() !== filter.category) {
        return false;
      }

      if (filter.tags && !filter.tags.some(tag => tool.getTags().includes(tag))) {
        return false;
      }

      if (filter.dangerous !== undefined && tool.isDangerous() !== filter.dangerous) {
        return false;
      }

      if (filter.requiresConfirmation !== undefined && tool.requiresConfirmation !== filter.requiresConfirmation) {
        return false;
      }

      if (filter.enabled !== undefined) {
        const isEnabled = this.isToolEnabled(tool.name);
        if (isEnabled !== filter.enabled) {
          return false;
        }
      }

      return true;
    });
  }

  public enableTool(toolName: string): boolean {
    if (!this.tools.has(toolName)) {
      return false;
    }

    this.enabledTools.add(toolName);
    this.disabledTools.delete(toolName);
    return true;
  }

  public disableTool(toolName: string): boolean {
    if (!this.tools.has(toolName)) {
      return false;
    }

    this.disabledTools.add(toolName);
    this.enabledTools.delete(toolName);
    return true;
  }

  public isToolEnabled(toolName: string): boolean {
    return this.enabledTools.has(toolName) && !this.disabledTools.has(toolName);
  }

  public isToolRegistered(toolName: string): boolean {
    return this.tools.has(toolName);
  }

  public async validateToolExecution(
    toolName: string,
    parameters: Record<string, any>
  ): Promise<ToolValidationResult> {
    const tool = this.getTool(toolName);
    if (!tool) {
      return {
        valid: false,
        errors: [`Tool '${toolName}' is not registered`],
        warnings: [],
      };
    }

    if (!this.isToolEnabled(toolName)) {
      return {
        valid: false,
        errors: [`Tool '${toolName}' is disabled`],
        warnings: [],
      };
    }

    return await tool.validate(parameters);
  }

  public async executeTool(
    toolName: string,
    parameters: Record<string, any>,
    context: ToolExecutionContext = {}
  ): Promise<ToolExecutionResult> {
    const startTime = Date.now();

    try {
      // Validate tool execution
      const validation = await this.validateToolExecution(toolName, parameters);
      if (!validation.valid) {
        const result: ToolExecutionResult = {
          success: false,
          content: '',
          error: `Validation failed: ${validation.errors.join(', ')}`,
          toolName,
          executionTime: Date.now() - startTime,
          context,
        };
        this.addToHistory(result);
        return result;
      }

      const tool = this.getTool(toolName)!;

      // Execute the tool
      const toolResult = await tool.execute(parameters);

      const result: ToolExecutionResult = {
        ...toolResult,
        toolName,
        executionTime: Date.now() - startTime,
        context,
      };

      this.addToHistory(result);
      return result;

    } catch (error) {
      const result: ToolExecutionResult = {
        success: false,
        content: '',
        error: `Execution failed: ${error instanceof Error ? error.message : String(error)}`,
        toolName,
        executionTime: Date.now() - startTime,
        context,
      };
      this.addToHistory(result);
      return result;
    }
  }

  public async shouldConfirmExecution(
    toolName: string,
    parameters: Record<string, any>
  ): Promise<boolean> {
    const tool = this.getTool(toolName);
    if (!tool) {
      return true; // Confirm unknown tools
    }

    return await tool.shouldConfirmExecute(parameters);
  }

  public getExecutionHistory(limit?: number): ToolExecutionResult[] {
    const history = [...this.toolExecutionHistory];
    return limit ? history.slice(-limit) : history;
  }

  public clearExecutionHistory(): void {
    this.toolExecutionHistory = [];
  }

  public getToolStatistics(): Record<string, {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    lastExecuted?: Date;
  }> {
    const stats: Record<string, any> = {};

    for (const tool of this.getAllTools()) {
      const toolHistory = this.toolExecutionHistory.filter(h => h.toolName === tool.name);
      
      if (toolHistory.length === 0) {
        stats[tool.name] = {
          totalExecutions: 0,
          successfulExecutions: 0,
          failedExecutions: 0,
          averageExecutionTime: 0,
        };
        continue;
      }

      const successful = toolHistory.filter(h => h.success);
      const failed = toolHistory.filter(h => !h.success);
      const totalTime = toolHistory.reduce((sum, h) => sum + h.executionTime, 0);
      const lastExecution = toolHistory[toolHistory.length - 1];

      stats[tool.name] = {
        totalExecutions: toolHistory.length,
        successfulExecutions: successful.length,
        failedExecutions: failed.length,
        averageExecutionTime: totalTime / toolHistory.length,
        lastExecuted: new Date(Date.now() - lastExecution.executionTime),
      };
    }

    return stats;
  }

  public exportToolDefinitions(): Array<{
    name: string;
    description: string;
    parameters: Record<string, any>;
    metadata: ToolMetadata;
  }> {
    return this.getEnabledTools().map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters,
      metadata: tool.getMetadata(),
    }));
  }

  public getToolCount(): number {
    return this.tools.size;
  }

  public getEnabledToolCount(): number {
    return this.getEnabledTools().length;
  }

  private addToHistory(result: ToolExecutionResult): void {
    this.toolExecutionHistory.push(result);
    
    // Maintain history size limit
    if (this.toolExecutionHistory.length > this.maxHistorySize) {
      this.toolExecutionHistory = this.toolExecutionHistory.slice(-this.maxHistorySize);
    }
  }

  public setMaxHistorySize(size: number): void {
    this.maxHistorySize = Math.max(0, size);
    
    // Trim current history if needed
    if (this.toolExecutionHistory.length > this.maxHistorySize) {
      this.toolExecutionHistory = this.toolExecutionHistory.slice(-this.maxHistorySize);
    }
  }

  public clear(): void {
    this.tools.clear();
    this.enabledTools.clear();
    this.disabledTools.clear();
    this.toolExecutionHistory = [];
  }
}
