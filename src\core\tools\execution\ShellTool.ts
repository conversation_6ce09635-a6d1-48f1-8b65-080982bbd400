import { spawn, ChildProcess } from 'child_process';
import * as path from 'path';
import * as os from 'os';
import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';

export interface ShellExecutionResult {
  stdout: string;
  stderr: string;
  exitCode: number;
  duration: number;
  command: string;
  workingDirectory: string;
  processId?: number;
}

export class ShellTool extends BaseTool {
  public name = 'run_shell_command';
  public description = 'Executes shell commands with sandboxing and security controls';
  public requiresConfirmation = true;

  public parameters = {
    type: 'object',
    properties: {
      command: {
        type: 'string',
        description: 'The shell command to execute',
      },
      workingDirectory: {
        type: 'string',
        description: 'Working directory for command execution (optional)',
      },
      timeout: {
        type: 'number',
        description: 'Timeout in milliseconds (default: 30000)',
        default: 30000,
      },
      environment: {
        type: 'object',
        description: 'Additional environment variables',
        additionalProperties: { type: 'string' },
      },
      description: {
        type: 'string',
        description: 'Human-readable description of what the command does',
      },
    },
    required: ['command'],
  };

  private blockedCommands = [
    'rm', 'rmdir', 'del', 'delete',
    'sudo', 'su', 'chmod', 'chown',
    'dd', 'mkfs', 'fdisk',
    'shutdown', 'reboot', 'halt',
    'kill', 'killall', 'pkill',
    'format', 'diskpart',
    'reg', 'regedit',
    'systemctl', 'service',
    'crontab', 'at',
    'passwd', 'usermod', 'useradd', 'userdel',
    'mount', 'umount',
    'iptables', 'ufw', 'firewall-cmd'
  ];

  private allowedCommands = [
    'ls', 'dir', 'cat', 'type', 'head', 'tail',
    'grep', 'find', 'locate', 'which', 'where',
    'pwd', 'cd', 'echo', 'printf',
    'wc', 'sort', 'uniq', 'cut', 'awk', 'sed',
    'curl', 'wget', 'ping', 'nslookup', 'dig',
    'git', 'npm', 'yarn', 'pip', 'cargo', 'go',
    'node', 'python', 'python3', 'java', 'javac',
    'gcc', 'g++', 'make', 'cmake',
    'docker', 'kubectl',
    'ps', 'top', 'htop', 'free', 'df', 'du',
    'date', 'cal', 'uptime', 'whoami', 'id',
    'env', 'printenv', 'export', 'set',
    'history', 'alias', 'unalias'
  ];

  constructor() {
    super({
      category: 'execution',
      tags: ['shell', 'command', 'execution', 'system'],
      version: '1.0.0',
      dangerous: true,
      requiresConfirmation: true,
    });
  }

  public async validate(params: Record<string, any>): Promise<ToolValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!params.command || typeof params.command !== 'string') {
      errors.push('Command is required and must be a string');
    } else {
      const command = params.command.trim();
      
      if (command.length === 0) {
        errors.push('Command cannot be empty');
      }

      // Check for blocked commands
      const commandParts = command.split(/\s+/);
      const baseCommand = commandParts[0].toLowerCase();
      
      if (this.blockedCommands.some(blocked => baseCommand.includes(blocked))) {
        errors.push(`Command '${baseCommand}' is blocked for security reasons`);
      }

      // Check for dangerous patterns
      if (command.includes('&&') || command.includes('||') || command.includes(';')) {
        warnings.push('Command contains chaining operators - use with caution');
      }

      if (command.includes('|')) {
        warnings.push('Command contains pipes - ensure all parts are safe');
      }

      if (command.includes('>') || command.includes('>>')) {
        warnings.push('Command contains output redirection');
      }

      if (command.includes('$(') || command.includes('`')) {
        warnings.push('Command contains command substitution');
      }

      // Check working directory
      if (params.workingDirectory) {
        if (typeof params.workingDirectory !== 'string') {
          errors.push('Working directory must be a string');
        } else {
          const workDir = path.resolve(params.workingDirectory);
          const homeDir = os.homedir();
          const currentDir = process.cwd();
          
          // Only allow execution in home directory or current working directory subtrees
          if (!workDir.startsWith(homeDir) && !workDir.startsWith(currentDir)) {
            errors.push('Working directory must be within home directory or current working directory');
          }
        }
      }

      // Check timeout
      if (params.timeout !== undefined) {
        if (typeof params.timeout !== 'number' || params.timeout <= 0 || params.timeout > 300000) {
          errors.push('Timeout must be a positive number not exceeding 300000ms (5 minutes)');
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  public async execute(params: Record<string, any>): Promise<ToolResult> {
    const validation = await this.validate(params);
    if (!validation.valid) {
      return this.createErrorResult(`Validation failed: ${validation.errors.join(', ')}`);
    }

    const command = params.command.trim();
    const workingDirectory = params.workingDirectory ? path.resolve(params.workingDirectory) : process.cwd();
    const timeout = params.timeout || 30000;
    const environment = { ...process.env, ...(params.environment || {}) };

    try {
      const result = await this.executeCommand(command, workingDirectory, timeout, environment);
      
      const displayContent = this.formatExecutionResult(result);
      const metadata = {
        command: result.command,
        workingDirectory: result.workingDirectory,
        exitCode: result.exitCode,
        duration: result.duration,
        processId: result.processId,
      };

      if (result.exitCode === 0) {
        return this.createSuccessResult(result.stdout, displayContent, metadata);
      } else {
        return this.createErrorResult(
          `Command failed with exit code ${result.exitCode}: ${result.stderr}`,
          metadata
        );
      }
    } catch (error) {
      return this.createErrorResult(`Execution failed: ${error}`);
    }
  }

  private async executeCommand(
    command: string,
    workingDirectory: string,
    timeout: number,
    environment: Record<string, string>
  ): Promise<ShellExecutionResult> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      // Determine shell and command format based on platform
      const isWindows = os.platform() === 'win32';
      const shell = isWindows ? 'cmd.exe' : '/bin/bash';
      const shellArgs = isWindows ? ['/c', command] : ['-c', command];

      const childProcess: ChildProcess = spawn(shell, shellArgs, {
        cwd: workingDirectory,
        env: environment,
        stdio: ['pipe', 'pipe', 'pipe'],
        detached: false,
        windowsHide: true,
      });

      let stdout = '';
      let stderr = '';
      let timeoutHandle: NodeJS.Timeout;

      // Set up timeout
      timeoutHandle = setTimeout(() => {
        childProcess.kill('SIGTERM');
        setTimeout(() => {
          if (!childProcess.killed) {
            childProcess.kill('SIGKILL');
          }
        }, 5000);
        reject(new Error(`Command timed out after ${timeout}ms`));
      }, timeout);

      // Collect output
      if (childProcess.stdout) {
        childProcess.stdout.on('data', (data) => {
          stdout += data.toString();
        });
      }

      if (childProcess.stderr) {
        childProcess.stderr.on('data', (data) => {
          stderr += data.toString();
        });
      }

      // Handle process completion
      childProcess.on('close', (exitCode) => {
        clearTimeout(timeoutHandle);
        const duration = Date.now() - startTime;
        
        resolve({
          stdout: stdout.trim(),
          stderr: stderr.trim(),
          exitCode: exitCode || 0,
          duration,
          command,
          workingDirectory,
          processId: childProcess.pid,
        });
      });

      childProcess.on('error', (error) => {
        clearTimeout(timeoutHandle);
        reject(error);
      });
    });
  }

  private formatExecutionResult(result: ShellExecutionResult): string {
    const lines = [
      `Command: ${result.command}`,
      `Working Directory: ${result.workingDirectory}`,
      `Exit Code: ${result.exitCode}`,
      `Duration: ${result.duration}ms`,
      '',
    ];

    if (result.stdout) {
      lines.push('STDOUT:', '```', result.stdout, '```', '');
    }

    if (result.stderr) {
      lines.push('STDERR:', '```', result.stderr, '```', '');
    }

    return lines.join('\n');
  }

  public async shouldConfirmExecute(params: Record<string, any>): Promise<boolean> {
    // Always require confirmation for shell commands unless explicitly disabled
    const command = params.command?.trim() || '';
    const baseCommand = command.split(/\s+/)[0].toLowerCase();
    
    // Some safe commands might not need confirmation
    const safeCommands = ['ls', 'dir', 'pwd', 'whoami', 'date', 'echo'];
    if (safeCommands.includes(baseCommand) && !command.includes('|') && !command.includes('>')) {
      return false;
    }

    return true;
  }
}
