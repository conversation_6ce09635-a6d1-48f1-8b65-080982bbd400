import * as fs from 'fs-extra';
import * as path from 'path';
import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';

export class EditTool extends BaseTool {
  public name = 'replace';
  public description = 'Performs precise text replacements in files with validation and preview';
  public requiresConfirmation = true;

  public parameters = {
    type: 'object',
    properties: {
      file_path: {
        type: 'string',
        description: 'Path to the file to edit (absolute or relative)',
      },
      old_string: {
        type: 'string',
        description: 'Exact text to find and replace (include surrounding context for precision)',
      },
      new_string: {
        type: 'string',
        description: 'Text to replace the old string with',
      },
      expected_replacements: {
        type: 'number',
        description: 'Expected number of replacements (for validation)',
        minimum: 1,
        default: 1,
      },
      case_sensitive: {
        type: 'boolean',
        description: 'Whether the search should be case sensitive',
        default: true,
      },
      whole_words_only: {
        type: 'boolean',
        description: 'Whether to match whole words only',
        default: false,
      },
      backup_original: {
        type: 'boolean',
        description: 'Whether to create a backup of the original file',
        default: true,
      },
      dry_run: {
        type: 'boolean',
        description: 'Preview changes without actually modifying the file',
        default: false,
      },
    },
    required: ['file_path', 'old_string', 'new_string'],
  };

  constructor() {
    super({
      category: 'filesystem',
      tags: ['file', 'edit', 'replace', 'modify'],
      version: '1.0.0',
      dangerous: true,
      requiresConfirmation: true,
    });
  }

  public async validate(params: Record<string, any>): Promise<ToolValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate required parameters
    const requiredValidation = this.validateRequiredParams(params, ['file_path', 'old_string', 'new_string']);
    if (!requiredValidation.valid) {
      return requiredValidation;
    }

    // Validate file path
    const pathErrors = this.validateStringParam(params.file_path, 'file_path');
    errors.push(...pathErrors);

    // Validate strings
    const oldStringErrors = this.validateStringParam(params.old_string, 'old_string');
    errors.push(...oldStringErrors);

    const newStringErrors = this.validateStringParam(params.new_string, 'new_string', {
      allowEmpty: true, // Allow empty replacement (deletion)
    });
    errors.push(...newStringErrors);

    // Validate optional parameters
    if (params.expected_replacements !== undefined) {
      errors.push(...this.validateNumberParam(params.expected_replacements, 'expected_replacements', {
        min: 1,
        integer: true,
      }));
    }

    if (params.case_sensitive !== undefined) {
      errors.push(...this.validateBooleanParam(params.case_sensitive, 'case_sensitive'));
    }

    if (params.whole_words_only !== undefined) {
      errors.push(...this.validateBooleanParam(params.whole_words_only, 'whole_words_only'));
    }

    if (params.backup_original !== undefined) {
      errors.push(...this.validateBooleanParam(params.backup_original, 'backup_original'));
    }

    if (params.dry_run !== undefined) {
      errors.push(...this.validateBooleanParam(params.dry_run, 'dry_run'));
    }

    // Check file existence and permissions
    try {
      const resolvedPath = path.resolve(params.file_path);
      const stats = await fs.stat(resolvedPath);
      
      if (!stats.isFile()) {
        errors.push(`Path '${params.file_path}' is not a file`);
      } else {
        // Check if file is readable
        try {
          await fs.access(resolvedPath, fs.constants.R_OK);
        } catch {
          errors.push(`File '${params.file_path}' is not readable`);
        }

        // Check if file is writable (unless dry run)
        if (!params.dry_run) {
          try {
            await fs.access(resolvedPath, fs.constants.W_OK);
          } catch {
            errors.push(`File '${params.file_path}' is not writable`);
          }
        }

        // Warn about large files
        if (stats.size > 10 * 1024 * 1024) { // 10MB
          warnings.push(`File is large (${this.formatFileSize(stats.size)}). Operation may take some time.`);
        }
      }
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        errors.push(`File '${params.file_path}' does not exist`);
      } else {
        errors.push(`Cannot access file '${params.file_path}': ${error}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  public async execute(params: Record<string, any>): Promise<ToolResult> {
    try {
      const {
        file_path,
        old_string,
        new_string,
        expected_replacements = 1,
        case_sensitive = true,
        whole_words_only = false,
        backup_original = true,
        dry_run = false,
      } = params;

      const resolvedPath = path.resolve(file_path);
      
      // Read the original content
      const originalContent = await fs.readFile(resolvedPath, 'utf8');
      
      // Perform the replacement
      const { newContent, replacementCount, changes } = this.performReplacement(
        originalContent,
        old_string,
        new_string,
        case_sensitive,
        whole_words_only
      );

      // Validate replacement count
      if (replacementCount !== expected_replacements) {
        return this.createErrorResult(
          `Expected ${expected_replacements} replacements but found ${replacementCount} matches`
        );
      }

      if (replacementCount === 0) {
        return this.createErrorResult(
          `No matches found for the specified text: "${this.truncateContent(old_string, 100)}"`
        );
      }

      // Create backup if requested and not dry run
      let backupPath: string | undefined;
      if (backup_original && !dry_run) {
        backupPath = await this.createBackup(resolvedPath);
      }

      // Write the new content (unless dry run)
      if (!dry_run) {
        await fs.writeFile(resolvedPath, newContent, 'utf8');
      }

      const stats = await fs.stat(resolvedPath);
      const metadata = {
        path: resolvedPath,
        originalSize: originalContent.length,
        newSize: newContent.length,
        replacementCount,
        backupPath,
        dryRun: dry_run,
        changes,
      };

      const displayContent = this.createDisplayContent(
        file_path,
        changes,
        replacementCount,
        dry_run,
        backupPath
      );

      const action = dry_run ? 'Preview of changes' : 'File edited successfully';
      
      return this.createSuccessResult(
        `${action}: ${replacementCount} replacement(s) made in ${file_path}`,
        displayContent,
        metadata
      );
    } catch (error) {
      return this.createErrorResult(
        `Failed to edit file: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private performReplacement(
    content: string,
    oldString: string,
    newString: string,
    caseSensitive: boolean,
    wholeWordsOnly: boolean
  ): {
    newContent: string;
    replacementCount: number;
    changes: Array<{
      lineNumber: number;
      oldLine: string;
      newLine: string;
      position: number;
    }>;
  } {
    const changes: Array<{
      lineNumber: number;
      oldLine: string;
      newLine: string;
      position: number;
    }> = [];

    let searchString = oldString;
    let flags = 'g'; // Global replacement
    
    if (!caseSensitive) {
      flags += 'i';
    }

    if (wholeWordsOnly) {
      // Escape special regex characters and add word boundaries
      searchString = '\\b' + oldString.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '\\b';
    } else {
      // Escape special regex characters
      searchString = oldString.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    const regex = new RegExp(searchString, flags);
    const lines = content.split('\n');
    let replacementCount = 0;
    
    // Track changes line by line for better reporting
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const matches = line.match(regex);
      
      if (matches) {
        const newLine = line.replace(regex, newString);
        if (newLine !== line) {
          changes.push({
            lineNumber: i + 1,
            oldLine: line,
            newLine: newLine,
            position: line.search(regex),
          });
          lines[i] = newLine;
          replacementCount += matches.length;
        }
      }
    }

    return {
      newContent: lines.join('\n'),
      replacementCount,
      changes,
    };
  }

  private async createBackup(filePath: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup.${timestamp}`;
    
    await fs.copy(filePath, backupPath);
    return backupPath;
  }

  private createDisplayContent(
    filePath: string,
    changes: Array<{
      lineNumber: number;
      oldLine: string;
      newLine: string;
      position: number;
    }>,
    replacementCount: number,
    dryRun: boolean,
    backupPath?: string
  ): string {
    const lines: string[] = [];
    
    lines.push(`${dryRun ? 'Preview of changes' : 'Changes made'} to: ${filePath}`);
    lines.push(`Replacements: ${replacementCount}`);
    
    if (backupPath) {
      lines.push(`Backup created: ${backupPath}`);
    }
    
    lines.push('');
    lines.push('Changes:');
    lines.push('─'.repeat(60));
    
    for (const change of changes.slice(0, 10)) { // Show first 10 changes
      lines.push(`Line ${change.lineNumber}:`);
      lines.push(`- ${change.oldLine}`);
      lines.push(`+ ${change.newLine}`);
      lines.push('');
    }
    
    if (changes.length > 10) {
      lines.push(`... and ${changes.length - 10} more changes`);
    }
    
    return lines.join('\n');
  }
}
