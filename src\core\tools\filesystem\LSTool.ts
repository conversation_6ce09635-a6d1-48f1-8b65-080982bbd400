import * as fs from 'fs-extra';
import * as path from 'path';
import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';

export class LSTool extends BaseTool {
  public name = 'list_directory';
  public description = 'Lists contents of directories with detailed information';
  public requiresConfirmation = false;

  public parameters = {
    type: 'object',
    properties: {
      path: {
        type: 'string',
        description: 'Directory path to list (absolute or relative)',
      },
      recursive: {
        type: 'boolean',
        description: 'Whether to list subdirectories recursively',
        default: false,
      },
      showHidden: {
        type: 'boolean',
        description: 'Whether to show hidden files and directories',
        default: false,
      },
      sortBy: {
        type: 'string',
        enum: ['name', 'size', 'modified', 'type'],
        description: 'Sort entries by specified criteria',
        default: 'name',
      },
      maxDepth: {
        type: 'number',
        description: 'Maximum depth for recursive listing',
        default: 3,
      },
    },
    required: ['path'],
  };

  constructor() {
    super({
      category: 'filesystem',
      tags: ['directory', 'listing', 'files'],
      version: '1.0.0',
      dangerous: false,
      requiresConfirmation: false,
    });
  }

  public async validate(params: Record<string, any>): Promise<ToolValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate required parameters
    const requiredValidation = this.validateRequiredParams(params, ['path']);
    if (!requiredValidation.valid) {
      return requiredValidation;
    }

    // Validate path parameter
    const pathErrors = this.validateStringParam(params.path, 'path');
    errors.push(...pathErrors);

    // Validate optional parameters
    if (params.recursive !== undefined) {
      errors.push(...this.validateBooleanParam(params.recursive, 'recursive'));
    }

    if (params.showHidden !== undefined) {
      errors.push(...this.validateBooleanParam(params.showHidden, 'showHidden'));
    }

    if (params.sortBy !== undefined) {
      const validSortOptions = ['name', 'size', 'modified', 'type'];
      if (!validSortOptions.includes(params.sortBy)) {
        errors.push(`Parameter 'sortBy' must be one of: ${validSortOptions.join(', ')}`);
      }
    }

    if (params.maxDepth !== undefined) {
      errors.push(...this.validateNumberParam(params.maxDepth, 'maxDepth', {
        min: 1,
        max: 10,
        integer: true,
      }));
    }

    // Check if path exists and is accessible
    try {
      const resolvedPath = path.resolve(params.path);
      const stats = await fs.stat(resolvedPath);
      
      if (!stats.isDirectory()) {
        errors.push(`Path '${params.path}' is not a directory`);
      }
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        errors.push(`Directory '${params.path}' does not exist`);
      } else if ((error as any).code === 'EACCES') {
        errors.push(`Permission denied accessing '${params.path}'`);
      } else {
        errors.push(`Cannot access directory '${params.path}': ${error}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  public async execute(params: Record<string, any>): Promise<ToolResult> {
    try {
      const {
        path: dirPath,
        recursive = false,
        showHidden = false,
        sortBy = 'name',
        maxDepth = 3,
      } = params;

      const resolvedPath = path.resolve(dirPath);
      const entries = await this.listDirectory(
        resolvedPath,
        recursive,
        showHidden,
        sortBy,
        maxDepth,
        0
      );

      const formattedOutput = this.formatDirectoryListing(entries, resolvedPath);
      const summary = this.createSummary(entries);

      return this.createSuccessResult(
        formattedOutput,
        `Listed ${entries.length} items in ${dirPath}`,
        {
          path: resolvedPath,
          totalItems: entries.length,
          summary,
        }
      );
    } catch (error) {
      return this.createErrorResult(
        `Failed to list directory: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private async listDirectory(
    dirPath: string,
    recursive: boolean,
    showHidden: boolean,
    sortBy: string,
    maxDepth: number,
    currentDepth: number
  ): Promise<DirectoryEntry[]> {
    const entries: DirectoryEntry[] = [];

    try {
      const items = await fs.readdir(dirPath);

      for (const item of items) {
        // Skip hidden files if not requested
        if (!showHidden && item.startsWith('.')) {
          continue;
        }

        const itemPath = path.join(dirPath, item);
        
        try {
          const stats = await fs.stat(itemPath);
          const entry: DirectoryEntry = {
            name: item,
            path: itemPath,
            relativePath: path.relative(process.cwd(), itemPath),
            type: stats.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            modified: stats.mtime,
            permissions: this.getPermissions(stats),
            depth: currentDepth,
          };

          entries.push(entry);

          // Recurse into subdirectories if requested
          if (recursive && stats.isDirectory() && currentDepth < maxDepth) {
            const subEntries = await this.listDirectory(
              itemPath,
              recursive,
              showHidden,
              sortBy,
              maxDepth,
              currentDepth + 1
            );
            entries.push(...subEntries);
          }
        } catch (error) {
          // Skip items that can't be accessed
          continue;
        }
      }
    } catch (error) {
      throw new Error(`Cannot read directory ${dirPath}: ${error}`);
    }

    return this.sortEntries(entries, sortBy);
  }

  private sortEntries(entries: DirectoryEntry[], sortBy: string): DirectoryEntry[] {
    return entries.sort((a, b) => {
      switch (sortBy) {
        case 'size':
          return b.size - a.size;
        case 'modified':
          return b.modified.getTime() - a.modified.getTime();
        case 'type':
          if (a.type !== b.type) {
            return a.type === 'directory' ? -1 : 1;
          }
          return a.name.localeCompare(b.name);
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });
  }

  private formatDirectoryListing(entries: DirectoryEntry[], basePath: string): string {
    if (entries.length === 0) {
      return `Directory ${basePath} is empty`;
    }

    const lines: string[] = [];
    lines.push(`Directory listing for: ${basePath}`);
    lines.push('');

    // Group by depth for better readability
    const byDepth = entries.reduce((acc, entry) => {
      if (!acc[entry.depth]) acc[entry.depth] = [];
      acc[entry.depth].push(entry);
      return acc;
    }, {} as Record<number, DirectoryEntry[]>);

    for (const depth of Object.keys(byDepth).sort((a, b) => parseInt(a) - parseInt(b))) {
      const depthEntries = byDepth[parseInt(depth)];
      
      for (const entry of depthEntries) {
        const indent = '  '.repeat(entry.depth);
        const typeIcon = entry.type === 'directory' ? '📁' : '📄';
        const size = entry.type === 'file' ? this.formatFileSize(entry.size) : '';
        const modified = entry.modified.toLocaleDateString();
        
        lines.push(
          `${indent}${typeIcon} ${entry.name} ${size} ${entry.permissions} ${modified}`
        );
      }
    }

    return lines.join('\n');
  }

  private createSummary(entries: DirectoryEntry[]): Record<string, number> {
    const summary = {
      totalFiles: 0,
      totalDirectories: 0,
      totalSize: 0,
    };

    for (const entry of entries) {
      if (entry.type === 'file') {
        summary.totalFiles++;
        summary.totalSize += entry.size;
      } else {
        summary.totalDirectories++;
      }
    }

    return summary;
  }

  private getPermissions(stats: fs.Stats): string {
    const mode = stats.mode;
    let permissions = '';

    // Owner permissions
    permissions += (mode & 0o400) ? 'r' : '-';
    permissions += (mode & 0o200) ? 'w' : '-';
    permissions += (mode & 0o100) ? 'x' : '-';

    // Group permissions
    permissions += (mode & 0o040) ? 'r' : '-';
    permissions += (mode & 0o020) ? 'w' : '-';
    permissions += (mode & 0o010) ? 'x' : '-';

    // Other permissions
    permissions += (mode & 0o004) ? 'r' : '-';
    permissions += (mode & 0o002) ? 'w' : '-';
    permissions += (mode & 0o001) ? 'x' : '-';

    return permissions;
  }
}

interface DirectoryEntry {
  name: string;
  path: string;
  relativePath: string;
  type: 'file' | 'directory';
  size: number;
  modified: Date;
  permissions: string;
  depth: number;
}
