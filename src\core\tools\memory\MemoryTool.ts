import * as fs from 'fs-extra';
import * as path from 'path';
import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';

export interface MemoryEntry {
  id: string;
  content: string;
  tags: string[];
  timestamp: Date;
  importance: number;
  context?: string;
}

export interface MemorySearchResult {
  entries: MemoryEntry[];
  totalMatches: number;
  searchTime: number;
}

export class MemoryTool extends BaseTool {
  public name = 'memory';
  public description = 'Stores, retrieves, and manages persistent memory across conversations';
  public requiresConfirmation = false;

  private memoryFile: string;

  public parameters = {
    type: 'object',
    properties: {
      action: {
        type: 'string',
        enum: ['store', 'retrieve', 'search', 'list', 'delete', 'update'],
        description: 'Action to perform with memory',
      },
      content: {
        type: 'string',
        description: 'Content to store or update (required for store/update actions)',
      },
      id: {
        type: 'string',
        description: 'Memory entry ID (required for retrieve/delete/update actions)',
      },
      query: {
        type: 'string',
        description: 'Search query (required for search action)',
      },
      tags: {
        type: 'array',
        items: { type: 'string' },
        description: 'Tags to associate with memory entry',
      },
      importance: {
        type: 'number',
        description: 'Importance level (1-10, default: 5)',
        minimum: 1,
        maximum: 10,
        default: 5,
      },
      context: {
        type: 'string',
        description: 'Additional context for the memory entry',
      },
      limit: {
        type: 'number',
        description: 'Maximum number of results to return',
        default: 10,
        minimum: 1,
        maximum: 100,
      },
    },
    required: ['action'],
  };

  constructor(memoryDir?: string) {
    super({
      category: 'memory',
      tags: ['memory', 'storage', 'persistence'],
      version: '1.0.0',
      dangerous: false,
      requiresConfirmation: false,
    });

    const defaultMemoryDir = path.join(process.cwd(), '.ai-cli-memory');
    this.memoryFile = path.join(memoryDir || defaultMemoryDir, 'memory.json');
  }

  public async validate(params: Record<string, any>): Promise<ToolValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate required parameters
    const requiredValidation = this.validateRequiredParams(params, ['action']);
    if (!requiredValidation.valid) {
      return requiredValidation;
    }

    const action = params.action;
    const validActions = ['store', 'retrieve', 'search', 'list', 'delete', 'update'];
    
    if (!validActions.includes(action)) {
      errors.push(`Action must be one of: ${validActions.join(', ')}`);
    }

    // Validate action-specific requirements
    switch (action) {
      case 'store':
        if (!params.content) {
          errors.push('Content is required for store action');
        }
        break;
      case 'retrieve':
      case 'delete':
        if (!params.id) {
          errors.push(`ID is required for ${action} action`);
        }
        break;
      case 'update':
        if (!params.id) {
          errors.push('ID is required for update action');
        }
        if (!params.content) {
          errors.push('Content is required for update action');
        }
        break;
      case 'search':
        if (!params.query) {
          errors.push('Query is required for search action');
        }
        break;
    }

    // Validate optional parameters
    if (params.importance !== undefined) {
      errors.push(...this.validateNumberParam(params.importance, 'importance', {
        min: 1,
        max: 10,
        integer: true,
      }));
    }

    if (params.limit !== undefined) {
      errors.push(...this.validateNumberParam(params.limit, 'limit', {
        min: 1,
        max: 100,
        integer: true,
      }));
    }

    if (params.tags !== undefined) {
      errors.push(...this.validateArrayParam(params.tags, 'tags'));
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  public async execute(params: Record<string, any>): Promise<ToolResult> {
    try {
      const { action } = params;

      switch (action) {
        case 'store':
          return await this.storeMemory(params);
        case 'retrieve':
          return await this.retrieveMemory(params);
        case 'search':
          return await this.searchMemory(params);
        case 'list':
          return await this.listMemories(params);
        case 'delete':
          return await this.deleteMemory(params);
        case 'update':
          return await this.updateMemory(params);
        default:
          return this.createErrorResult(`Unknown action: ${action}`);
      }
    } catch (error) {
      return this.createErrorResult(
        `Memory operation failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private async storeMemory(params: Record<string, any>): Promise<ToolResult> {
    const {
      content,
      tags = [],
      importance = 5,
      context,
    } = params;

    const entry: MemoryEntry = {
      id: this.generateId(),
      content,
      tags,
      importance,
      context,
      timestamp: new Date(),
    };

    const memories = await this.loadMemories();
    memories.push(entry);
    await this.saveMemories(memories);

    return this.createSuccessResult(
      JSON.stringify(entry, null, 2),
      `Memory stored with ID: ${entry.id}`,
      { id: entry.id, action: 'store' }
    );
  }

  private async retrieveMemory(params: Record<string, any>): Promise<ToolResult> {
    const { id } = params;
    const memories = await this.loadMemories();
    const entry = memories.find(m => m.id === id);

    if (!entry) {
      return this.createErrorResult(`Memory entry with ID ${id} not found`);
    }

    return this.createSuccessResult(
      JSON.stringify(entry, null, 2),
      this.formatMemoryEntry(entry),
      { id: entry.id, action: 'retrieve' }
    );
  }

  private async searchMemory(params: Record<string, any>): Promise<ToolResult> {
    const { query, limit = 10 } = params;
    const startTime = Date.now();
    
    const memories = await this.loadMemories();
    const searchTerms = query.toLowerCase().split(/\s+/);
    
    const matches = memories.filter(entry => {
      const searchText = `${entry.content} ${entry.tags.join(' ')} ${entry.context || ''}`.toLowerCase();
      return searchTerms.some(term => searchText.includes(term));
    });

    // Sort by relevance (importance and recency)
    matches.sort((a, b) => {
      const scoreA = a.importance + (Date.now() - a.timestamp.getTime()) / (1000 * 60 * 60 * 24); // Decay over days
      const scoreB = b.importance + (Date.now() - b.timestamp.getTime()) / (1000 * 60 * 60 * 24);
      return scoreB - scoreA;
    });

    const results = matches.slice(0, limit);
    const searchResult: MemorySearchResult = {
      entries: results,
      totalMatches: matches.length,
      searchTime: Date.now() - startTime,
    };

    return this.createSuccessResult(
      JSON.stringify(searchResult, null, 2),
      this.formatSearchResults(searchResult, query),
      { 
        query,
        totalMatches: searchResult.totalMatches,
        returnedResults: results.length,
        action: 'search'
      }
    );
  }

  private async listMemories(params: Record<string, any>): Promise<ToolResult> {
    const { limit = 10 } = params;
    const memories = await this.loadMemories();
    
    // Sort by timestamp (most recent first)
    const sorted = memories.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    const limited = sorted.slice(0, limit);

    const formatted = this.formatMemoryList(limited, memories.length);

    return this.createSuccessResult(
      JSON.stringify(limited, null, 2),
      formatted,
      { 
        totalMemories: memories.length,
        returnedMemories: limited.length,
        action: 'list'
      }
    );
  }

  private async deleteMemory(params: Record<string, any>): Promise<ToolResult> {
    const { id } = params;
    const memories = await this.loadMemories();
    const index = memories.findIndex(m => m.id === id);

    if (index === -1) {
      return this.createErrorResult(`Memory entry with ID ${id} not found`);
    }

    const deleted = memories.splice(index, 1)[0];
    await this.saveMemories(memories);

    return this.createSuccessResult(
      JSON.stringify(deleted, null, 2),
      `Memory entry ${id} deleted successfully`,
      { id, action: 'delete' }
    );
  }

  private async updateMemory(params: Record<string, any>): Promise<ToolResult> {
    const { id, content, tags, importance, context } = params;
    const memories = await this.loadMemories();
    const entry = memories.find(m => m.id === id);

    if (!entry) {
      return this.createErrorResult(`Memory entry with ID ${id} not found`);
    }

    // Update fields
    if (content !== undefined) entry.content = content;
    if (tags !== undefined) entry.tags = tags;
    if (importance !== undefined) entry.importance = importance;
    if (context !== undefined) entry.context = context;
    entry.timestamp = new Date(); // Update timestamp

    await this.saveMemories(memories);

    return this.createSuccessResult(
      JSON.stringify(entry, null, 2),
      `Memory entry ${id} updated successfully`,
      { id, action: 'update' }
    );
  }

  private async loadMemories(): Promise<MemoryEntry[]> {
    try {
      await fs.ensureDir(path.dirname(this.memoryFile));
      
      if (await fs.pathExists(this.memoryFile)) {
        const data = await fs.readJson(this.memoryFile);
        return data.map((entry: any) => ({
          ...entry,
          timestamp: new Date(entry.timestamp),
        }));
      }
      
      return [];
    } catch (error) {
      throw new Error(`Failed to load memories: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async saveMemories(memories: MemoryEntry[]): Promise<void> {
    try {
      await fs.ensureDir(path.dirname(this.memoryFile));
      await fs.writeJson(this.memoryFile, memories, { spaces: 2 });
    } catch (error) {
      throw new Error(`Failed to save memories: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private formatMemoryEntry(entry: MemoryEntry): string {
    const lines: string[] = [];
    lines.push(`Memory Entry: ${entry.id}`);
    lines.push(`Timestamp: ${entry.timestamp.toLocaleString()}`);
    lines.push(`Importance: ${entry.importance}/10`);
    
    if (entry.tags.length > 0) {
      lines.push(`Tags: ${entry.tags.join(', ')}`);
    }
    
    if (entry.context) {
      lines.push(`Context: ${entry.context}`);
    }
    
    lines.push('');
    lines.push('Content:');
    lines.push('─'.repeat(40));
    lines.push(entry.content);
    
    return lines.join('\n');
  }

  private formatSearchResults(result: MemorySearchResult, query: string): string {
    const lines: string[] = [];
    lines.push(`Memory Search Results for: "${query}"`);
    lines.push(`Found ${result.totalMatches} matches (showing ${result.entries.length})`);
    lines.push(`Search time: ${result.searchTime}ms`);
    lines.push('');

    for (let i = 0; i < result.entries.length; i++) {
      const entry = result.entries[i];
      lines.push(`${i + 1}. ${entry.id} (Importance: ${entry.importance}/10)`);
      lines.push(`   ${entry.timestamp.toLocaleDateString()}`);
      lines.push(`   ${this.truncateContent(entry.content, 100)}`);
      if (entry.tags.length > 0) {
        lines.push(`   Tags: ${entry.tags.join(', ')}`);
      }
      lines.push('');
    }

    return lines.join('\n');
  }

  private formatMemoryList(memories: MemoryEntry[], total: number): string {
    const lines: string[] = [];
    lines.push(`Memory List (${memories.length} of ${total} total)`);
    lines.push('');

    for (let i = 0; i < memories.length; i++) {
      const entry = memories[i];
      lines.push(`${i + 1}. ${entry.id}`);
      lines.push(`   ${entry.timestamp.toLocaleDateString()} - Importance: ${entry.importance}/10`);
      lines.push(`   ${this.truncateContent(entry.content, 80)}`);
      if (entry.tags.length > 0) {
        lines.push(`   Tags: ${entry.tags.join(', ')}`);
      }
      lines.push('');
    }

    return lines.join('\n');
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}
