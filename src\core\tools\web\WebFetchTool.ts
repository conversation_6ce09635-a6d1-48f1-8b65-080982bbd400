import axios, { AxiosResponse } from 'axios';
import { URL } from 'url';
import { BaseTool, ToolValidationResult } from '../base/BaseTool';
import { ToolResult } from '../../types';

export interface WebFetchResult {
  url: string;
  statusCode: number;
  headers: Record<string, string>;
  content: string;
  contentType: string;
  size: number;
  redirects: string[];
}

export class WebFetchTool extends BaseTool {
  public name = 'web_fetch';
  public description = 'Fetches and processes content from URLs with support for various content types';
  public requiresConfirmation = false;

  public parameters = {
    type: 'object',
    properties: {
      url: {
        type: 'string',
        description: 'URL to fetch content from',
      },
      method: {
        type: 'string',
        enum: ['GET', 'POST', 'PUT', 'DELETE', 'HEAD'],
        description: 'HTTP method to use',
        default: 'GET',
      },
      headers: {
        type: 'object',
        description: 'Additional HTTP headers to send',
        additionalProperties: { type: 'string' },
      },
      body: {
        type: 'string',
        description: 'Request body for POST/PUT requests',
      },
      timeout: {
        type: 'number',
        description: 'Request timeout in milliseconds',
        default: 30000,
      },
      follow_redirects: {
        type: 'boolean',
        description: 'Whether to follow HTTP redirects',
        default: true,
      },
      max_redirects: {
        type: 'number',
        description: 'Maximum number of redirects to follow',
        default: 5,
      },
      user_agent: {
        type: 'string',
        description: 'User agent string to use',
        default: 'AI-CLI-Terminal/1.0',
      },
      extract_text: {
        type: 'boolean',
        description: 'Whether to extract text from HTML content',
        default: true,
      },
      max_content_size: {
        type: 'number',
        description: 'Maximum content size to download in bytes',
        default: 10485760, // 10MB
      },
    },
    required: ['url'],
  };

  constructor() {
    super({
      category: 'web',
      tags: ['http', 'fetch', 'web', 'download'],
      version: '1.0.0',
      dangerous: false,
      requiresConfirmation: false,
    });
  }

  public async validate(params: Record<string, any>): Promise<ToolValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate required parameters
    const requiredValidation = this.validateRequiredParams(params, ['url']);
    if (!requiredValidation.valid) {
      return requiredValidation;
    }

    // Validate URL
    try {
      const url = new URL(params.url);
      
      // Check protocol
      if (!['http:', 'https:'].includes(url.protocol)) {
        errors.push('URL must use HTTP or HTTPS protocol');
      }

      // Check for localhost/private IPs (security consideration)
      if (url.hostname === 'localhost' || 
          url.hostname === '127.0.0.1' || 
          url.hostname.startsWith('192.168.') ||
          url.hostname.startsWith('10.') ||
          url.hostname.startsWith('172.')) {
        warnings.push('Accessing local/private network addresses');
      }
    } catch (error) {
      errors.push(`Invalid URL format: ${error instanceof Error ? error.message : String(error)}`);
    }

    // Validate optional parameters
    if (params.method !== undefined) {
      const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'HEAD'];
      if (!validMethods.includes(params.method)) {
        errors.push(`Method must be one of: ${validMethods.join(', ')}`);
      }
    }

    if (params.timeout !== undefined) {
      errors.push(...this.validateNumberParam(params.timeout, 'timeout', {
        min: 1000,
        max: 300000, // 5 minutes max
      }));
    }

    if (params.max_redirects !== undefined) {
      errors.push(...this.validateNumberParam(params.max_redirects, 'max_redirects', {
        min: 0,
        max: 20,
        integer: true,
      }));
    }

    if (params.max_content_size !== undefined) {
      errors.push(...this.validateNumberParam(params.max_content_size, 'max_content_size', {
        min: 1024, // 1KB minimum
        max: 104857600, // 100MB maximum
      }));
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  public async execute(params: Record<string, any>): Promise<ToolResult> {
    try {
      const {
        url,
        method = 'GET',
        headers = {},
        body,
        timeout = 30000,
        follow_redirects = true,
        max_redirects = 5,
        user_agent = 'AI-CLI-Terminal/1.0',
        extract_text = true,
        max_content_size = 10485760,
      } = params;

      const result = await this.fetchUrl({
        url,
        method,
        headers: {
          'User-Agent': user_agent,
          ...headers,
        },
        body,
        timeout,
        follow_redirects,
        max_redirects,
        max_content_size,
      });

      let processedContent = result.content;
      
      // Extract text from HTML if requested
      if (extract_text && result.contentType.includes('text/html')) {
        processedContent = this.extractTextFromHtml(result.content);
      }

      const displayContent = this.formatWebResponse(result, processedContent);

      return this.createSuccessResult(
        processedContent,
        displayContent,
        {
          url: result.url,
          statusCode: result.statusCode,
          contentType: result.contentType,
          size: result.size,
          redirects: result.redirects,
        }
      );
    } catch (error) {
      return this.createErrorResult(
        `Failed to fetch URL: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private async fetchUrl(options: {
    url: string;
    method: string;
    headers: Record<string, string>;
    body?: string;
    timeout: number;
    follow_redirects: boolean;
    max_redirects: number;
    max_content_size: number;
  }): Promise<WebFetchResult> {
    const redirects: string[] = [];
    let currentUrl = options.url;

    const axiosConfig = {
      method: options.method.toLowerCase() as any,
      url: currentUrl,
      headers: options.headers,
      data: options.body,
      timeout: options.timeout,
      maxRedirects: options.follow_redirects ? options.max_redirects : 0,
      maxContentLength: options.max_content_size,
      validateStatus: () => true, // Don't throw on any status code
    };

    const response: AxiosResponse = await axios(axiosConfig);

    return {
      url: response.config.url || currentUrl,
      statusCode: response.status,
      headers: response.headers as Record<string, string>,
      content: response.data,
      contentType: response.headers['content-type'] || 'unknown',
      size: Buffer.byteLength(response.data, 'utf8'),
      redirects,
    };
  }

  private extractTextFromHtml(html: string): string {
    // Simple HTML text extraction (remove tags)
    return html
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\s+/g, ' ')
      .trim();
  }

  private formatWebResponse(result: WebFetchResult, content: string): string {
    const lines: string[] = [];
    
    lines.push(`Web Fetch Result`);
    lines.push(`URL: ${result.url}`);
    lines.push(`Status: ${result.statusCode}`);
    lines.push(`Content-Type: ${result.contentType}`);
    lines.push(`Size: ${this.formatFileSize(result.size)}`);
    
    if (result.redirects.length > 0) {
      lines.push(`Redirects: ${result.redirects.join(' → ')}`);
    }
    
    lines.push('');
    lines.push('Content:');
    lines.push('─'.repeat(50));
    lines.push(this.truncateContent(content, 5000));
    
    return lines.join('\n');
  }
}
