#!/usr/bin/env node

import { ConfigManager } from './core/config/ConfigManager';
import { AuthManager } from './cli-interface/components/auth/AuthManager';
import { TerminalManager } from './cli-interface/components/terminal/TerminalManager';
import { CLIState, CLIConfig, DEFAULT_CLI_CONFIG } from './cli-interface/types';
import { ToolRegistry } from './core/tools/base/ToolRegistry';
import { LSTool } from './core/tools/filesystem/LSTool';
import { ReadFileTool } from './core/tools/filesystem/ReadFileTool';
import { WriteFileTool } from './core/tools/filesystem/WriteFileTool';
import { EditTool } from './core/tools/filesystem/EditTool';

class AICliTerminal {
  private configManager: ConfigManager;
  private toolRegistry: ToolRegistry;
  private state: CLIState;
  private config: CLIConfig;

  constructor() {
    this.configManager = new ConfigManager();
    this.toolRegistry = new ToolRegistry();
    this.config = DEFAULT_CLI_CONFIG;
    
    this.state = {
      currentView: 'auth',
      isAuthenticated: false,
      currentProvider: '',
      currentModel: '',
    };

    this.initializeTools();
  }

  private initializeTools(): void {
    // Register file system tools
    this.toolRegistry.registerTool(new LSTool());
    this.toolRegistry.registerTool(new ReadFileTool());
    this.toolRegistry.registerTool(new WriteFileTool());
    this.toolRegistry.registerTool(new EditTool());

    // TODO: Register other tool categories
    // - Execution tools (ShellTool)
    // - Web tools (WebFetchTool, WebSearchTool)
    // - Memory tools (MemoryTool)
    // - MCP tools (DiscoveredTool, DiscoveredMCPTool)
  }

  public async start(): Promise<void> {
    try {
      console.log('🚀 Starting AI CLI Terminal...\n');

      // Main application loop
      while (true) {
        switch (this.state.currentView) {
          case 'auth':
            await this.handleAuthFlow();
            break;
            
          case 'terminal':
            await this.handleTerminalFlow();
            break;
            
          case 'config':
            await this.handleConfigFlow();
            break;
            
          default:
            console.error('Unknown view state:', this.state.currentView);
            process.exit(1);
        }
      }
    } catch (error) {
      console.error('Fatal error:', error);
      process.exit(1);
    }
  }

  private async handleAuthFlow(): Promise<void> {
    const authManager = new AuthManager(this.state, this.config, this.configManager);
    await authManager.render();
    
    // Check if user completed authentication
    if (this.state.isAuthenticated) {
      this.state.currentView = 'terminal';
    }
  }

  private async handleTerminalFlow(): Promise<void> {
    const terminalManager = new TerminalManager(this.state, this.config, this.configManager);
    await terminalManager.render();
    
    // Terminal manager will update state when user wants to exit or change view
  }

  private async handleConfigFlow(): Promise<void> {
    // Return to auth for configuration
    this.state.currentView = 'auth';
  }
}

// Main entry point
async function main(): Promise<void> {
  const terminal = new AICliTerminal();
  await terminal.start();
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('\n\n👋 Goodbye!');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n\n👋 Goodbye!');
  process.exit(0);
});

// Start the application
if (require.main === module) {
  main().catch(error => {
    console.error('Failed to start AI CLI Terminal:', error);
    process.exit(1);
  });
}

export { AICliTerminal };
